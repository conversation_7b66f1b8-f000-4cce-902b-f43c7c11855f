# Import built-in modules
import os

# Import third-party modules
from maya import cmds

# Import local modules
from cgame_avatar_factory import constants as const
import cgame_avatar_factory.api.dna_utils as dna
import cgame_avatar_factory.merge.mesh_util as mesh_util


class PinchFaceCreate:
    """Create and save DNA file with pinch face data

    Handles loading DNA calibration data, updating joint and vertex positions,
    and saving the modified DNA file.
    """

    def __init__(self):
        """Initialize PinchFaceCreate with DNA path"""
        self.reader = dna.TemplateDna.get_reader()
        self.output_path = self.create_output_path()
        self.writer = dna.init_writer_from_reader(self.output_path, self.reader)
        self.mesh_list = dna.TemplateDna.get_mesh_name()

        self.run_joints_command()
        self.writer.write()

    def get_output_path(self) -> str:
        """Get the output path of the saved DNA file

        Returns:
            str: Path where DNA file was saved
        """
        return self.output_path

    def run_joints_command(self):
        """Update joint transforms in DNA file

        Gets current joint translations and rotations from <PERSON>
        and updates them in the DNA writer.

        Args:
            writer: DNA writer object
            reader: DNA reader object
        """
        joint_translations = []
        joint_rotations = []
        for i in range(self.reader.getJointCount()):
            joint_name = self.reader.getJointName(i)
            all_bones = cmds.ls(type="joint")
            if joint_name in all_bones:
                translation = cmds.xform(joint_name, query=True, translation=True)
                rotation = cmds.joint(joint_name, query=True, orientation=True)
                joint_translations.append(translation)
                joint_rotations.append(rotation)
        self.writer.setNeutralJointTranslations(joint_translations)
        self.writer.setNeutralJointRotations(joint_rotations)

    def run_vertex_command(self, reader, mesh_list, writer):
        """Update vertex positions in DNA file

        Gets current vertex positions from Maya meshes
        and updates them in the DNA writer.

        Args:
            reader: DNA reader object
            mesh_list: List of mesh names
            writer: DNA writer object
        """
        mesh_count = reader.getMeshCount()
        for i in range(mesh_count):
            name = reader.getMeshName(i)
            for m in mesh_list:
                if name == m:
                    vertices_position = []
                    vertices = cmds.ls("{}.vtx[*]".format(name), fl=True)
                    for vertex in vertices:
                        position = cmds.pointPosition(vertex, world=True)
                        vertices_position.append(position)
                    writer.setVertexPositions(i, vertices_position)

    def create_output_path(self) -> str:
        """Create output path for DNA file

        Creates data directory if needed and returns path
        for saving DNA file.

        Returns:
            str: Output path for DNA file
        """
        maya_workspace = mesh_util.get_maya_workspace()
        data_dir = os.path.join(maya_workspace, "data")
        output_path = os.path.join(data_dir, const.OUTPUT_DNA_NAME + ".dna")

        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        return output_path
