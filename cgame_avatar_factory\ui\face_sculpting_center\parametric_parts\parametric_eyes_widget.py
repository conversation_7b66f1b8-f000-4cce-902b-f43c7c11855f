"""
Parametric Eyes widget for controlling eyeball and cornea parameters.
"""
# Import built-in modules
import logging

# Import third-party modules
from dayu_widgets import MCheckBox
from dayu_widgets import MLabel
from dayu_widgets import MRadioButton
from dayu_widgets import <PERSON>lider
from dayu_widgets import <PERSON><PERSON>ool<PERSON>utton
from dayu_widgets import dayu_theme
from qtpy import Qt<PERSON><PERSON>
from qtpy import QtGui
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.api.parametric_parts.parametric_eyes import EyeSelection
from cgame_avatar_factory.api.parametric_parts.parametric_eyes import ParametricEyes
from cgame_avatar_factory.ui.components.layout import FramelessHLayout
from cgame_avatar_factory.ui.components.layout import FramelessVLayout
from cgame_avatar_factory.ui.mixin.style_mixin import MStyleMixin


class CoordinateWidget(QtWidgets.QWidget):
    """Widget for displaying a coordinate system with a draggable control point."""

    # Signal emitted when the control point is moved
    pointMoved = QtCore.Signal(QtCore.QPointF)

    def __init__(self, parent=None):
        super(CoordinateWidget, self).__init__(parent)
        self.setFixedSize(200, 200)  # Smaller size to fit in the UI
        self.control_point = QtCore.QPointF(0, 0)  # Initial position at origin
        self.dragging = False
        self.grid_size = 20  # Grid size in pixels
        self.point_radius = 5  # Control point radius
        self.point_color = QtGui.QColor("#1890FF")  # Default color for control point

        # Set up anti-aliasing
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground)
        self.setAutoFillBackground(True)

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # Draw background
        painter.fillRect(self.rect(), QtGui.QColor(35, 35, 35))

        # Draw grid
        self.draw_grid(painter)

        # Draw coordinate axes
        self.draw_axes(painter)

        # Draw control point
        self.draw_control_point(painter)

    def draw_grid(self, painter):
        painter.save()
        pen = QtGui.QPen(QtGui.QColor(60, 60, 60), 1, QtCore.Qt.DotLine)
        painter.setPen(pen)

        width = self.width()
        height = self.height()
        center = self.get_center()

        # Vertical grid lines
        x = center.x() % self.grid_size
        while x < width:
            painter.drawLine(x, 0, x, height)
            x += self.grid_size

        # Horizontal grid lines
        y = center.y() % self.grid_size
        while y < height:
            painter.drawLine(0, y, width, y)
            y += self.grid_size
        painter.restore()

    def draw_axes(self, painter):
        painter.save()
        center = self.get_center()

        # Draw X axis
        painter.setPen(QtGui.QPen(QtGui.QColor("#5E5E5E"), 2))
        painter.drawLine(0, center.y(), self.width(), center.y())

        # Draw Y axis
        painter.setPen(QtGui.QPen(QtGui.QColor("#5E5E5E"), 2))
        painter.drawLine(center.x(), 0, center.x(), self.height())

        painter.restore()

    def draw_control_point(self, painter):
        painter.save()
        painter.setPen(QtGui.QPen(self.point_color, 2))
        painter.setBrush(self.point_color)

        center = self.get_center()
        point_pos = QtCore.QPointF(
            center.x() + self.control_point.x(),
            center.y() - self.control_point.y(),  # Y axis is inverted
        )
        painter.drawEllipse(point_pos, self.point_radius, self.point_radius)
        painter.restore()

    def get_center(self):
        return QtCore.QPointF(self.width() / 2, self.height() / 2)

    def mousePressEvent(self, event):
        center = self.get_center()
        widget_pos = event.pos()

        # Convert to coordinate system (center is origin, Y axis points up)
        point_pos = QtCore.QPointF(
            widget_pos.x() - center.x(),
            center.y() - widget_pos.y(),
        )

        # Limit to visible range
        max_x = center.x() - self.point_radius
        max_y = center.y() - self.point_radius
        new_x = max(-max_x, min(point_pos.x(), max_x))
        new_y = max(-max_y, min(point_pos.y(), max_y))

        # Move control point to clicked position
        self.control_point = QtCore.QPointF(new_x, new_y)
        self.pointMoved.emit(self.control_point)
        self.update()

        # Start dragging
        self.dragging = True

    def mouseMoveEvent(self, event):
        if self.dragging:
            center = self.get_center()
            widget_pos = event.pos()

            # Convert to logical coordinates
            new_x = widget_pos.x() - center.x()
            new_y = center.y() - widget_pos.y()

            # Limit to visible range
            max_x = center.x() - self.point_radius
            max_y = center.y() - self.point_radius
            new_x = max(-max_x, min(new_x, max_x))
            new_y = max(-max_y, min(new_y, max_y))

            self.control_point = QtCore.QPointF(new_x, new_y)
            self.pointMoved.emit(self.control_point)
            self.update()

    def mouseReleaseEvent(self, event):
        self.dragging = False

    def set_point_position(self, x, y):
        """Set the control point position programmatically.

        Args:
            x (float): X coordinate
            y (float): Y coordinate
        """
        center = self.get_center()
        max_x = center.x() - self.point_radius
        max_y = center.y() - self.point_radius

        # Limit to visible range
        x = max(-max_x, min(x, max_x))
        y = max(-max_y, min(y, max_y))

        self.control_point = QtCore.QPointF(x, y)
        self.update()

    def set_point_color(self, color):
        """Set the control point color.

        Args:
            color (str or QColor): The color to set for the control point
        """
        if isinstance(color, str):
            self.point_color = QtGui.QColor(color)
        else:
            self.point_color = color
        self.update()


class ParametricEyesWidget(QtWidgets.QWidget):
    """Widget for the eyeball section with symmetry, eyeball, and cornea controls.

    Contains three horizontal sections:
    1. Symmetry - with a checkbox
    2. Eyeball - with size and curvature sliders
    3. Cornea - with curvature and scale sliders
    """

    def __init__(self, parent=None):
        super(ParametricEyesWidget, self).__init__(parent)
        # Get the singleton instance of parametric eyes controller
        # With the metaclass implementation, we can directly use the class
        self.parametric_eyes = ParametricEyes()
        self.setup_ui()
        self.connect_signals()

    def create_section_title(self, text):
        """Create a standardized section title with consistent styling.

        Args:
            text (str): The title text

        Returns:
            MLabel: The styled title label
        """
        title = MStyleMixin.instance_wrapper(MLabel(text))
        title.foreground_color(dayu_theme.primary_text_color)
        title.setAlignment(QtCore.Qt.AlignCenter)

        # Set larger font size
        font = title.font()
        font.setPointSize(font.pointSize() + 2)  # Increase font size by 2 points
        font.setBold(True)  # Make the font bold
        title.setFont(font)

        # Add some padding around the title
        title.setContentsMargins(0, 5, 0, 5)

        # Add background style to make the title stand out and set fixed height
        title.setFixedHeight(50)  # Set fixed height to 50 pixels
        title.setStyleSheet(
            """
            QLabel {
                background-color: #3A3A3A;
                border-radius: 4px;
                padding: 4px;
                margin-top: 0px;
                margin-bottom: 8px;
                min-height: 50px;
                max-height: 50px;
            }
        """
        )

        return title

    def setup_ui(self):
        """Set up the user interface components."""
        self.main_layout = FramelessVLayout()
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.main_layout.setSpacing(15)
        self.setLayout(self.main_layout)

        # Create horizontal layout for the three sections
        self.horizontal_layout = FramelessHLayout()
        self.horizontal_layout.setSpacing(8)  # Add spacing between sections for clearer separation

        # Create the three sections
        self.setup_symmetry_section()
        self.setup_eyeball_section()
        self.setup_cornea_section()

        self.reset_layout = FramelessVLayout()

        self.reset_button = QtWidgets.QPushButton("重\n置\n眼\n球")
        self.reset_button.setMinimumWidth(60)
        self.reset_button.setStyleSheet(
            """
            QPushButton {
                letter-spacing: 8px;
                padding: 5px 2px;
                text-align: center;
                border: 2px solid #4A4A4A;
                border-radius: 8px;
                background-color: #3A3A3A;
                color: #CCCCCC;
            }
            QPushButton:hover {
                border-color: #5E5E5E;
                background-color: #4A4A4A;
            }
            QPushButton:pressed {
                background-color: #1A1A1A;
            }
        """
        )
        self.reset_button.setSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)

        self.reset_layout.addWidget(self.reset_button)
        self.horizontal_layout.addLayout(self.reset_layout)

        # Add horizontal layout to main layout
        self.main_layout.addLayout(self.horizontal_layout)

    def setup_symmetry_section(self):
        """Set up the symmetry section with a checkbox."""
        symmetry_container = QtWidgets.QWidget()
        # Add border and background style to the container
        symmetry_container.setStyleSheet(
            """
            QWidget {
                border: 2px solid #4A4A4A;
                border-radius: 6px;
                background-color: #2A2A2A;
                padding: 8px;
            }
        """
        )
        symmetry_layout = FramelessVLayout()
        symmetry_layout.setContentsMargins(10, 10, 10, 10)  # Increased margins for symmetry section
        symmetry_container.setLayout(symmetry_layout)

        # Section title with standardized styling
        symmetry_title = self.create_section_title("对称")
        symmetry_layout.addWidget(symmetry_title)

        # Checkbox with center alignment
        self.symmetry_checkbox = MStyleMixin.instance_wrapper(MCheckBox())
        self.symmetry_checkbox.setChecked(True)

        # Create a container widget for the checkbox to center it properly
        checkbox_container = QtWidgets.QWidget()
        checkbox_container.setContentsMargins(0, 0, 0, 0)

        # Create a horizontal layout for the container
        checkbox_layout = QtWidgets.QHBoxLayout(checkbox_container)
        checkbox_layout.setContentsMargins(0, 0, 0, 0)
        checkbox_layout.setAlignment(QtCore.Qt.AlignCenter)

        # Add the checkbox to the horizontal layout
        checkbox_layout.addWidget(self.symmetry_checkbox)

        # Apply style to center the checkbox indicator
        self.symmetry_checkbox.setStyleSheet(
            """
            QCheckBox {
                padding: 0px;
            }
            QCheckBox::indicator {
                subcontrol-position: center center;
            }
        """
        )

        # Add the container to the main vertical layout
        symmetry_layout.addWidget(checkbox_container, 0, QtCore.Qt.AlignCenter)

        # Create radio buttons for eye selection
        self.radio_group = QtWidgets.QButtonGroup(self)
        self.left_eye_radio = MStyleMixin.instance_wrapper(MRadioButton("左"))  # Left Eye
        self.right_eye_radio = MStyleMixin.instance_wrapper(MRadioButton("右"))  # Right Eye

        # Set the left eye as default selected
        self.left_eye_radio.setChecked(True)

        # Apply styling to radio buttons and increase font size
        radio_style = """
            QRadioButton {
                color: #CCCCCC;
                padding: 4px;
            }
            QRadioButton:checked {
                color: #1E90FF;
                font-weight: bold;
            }
            QRadioButton:disabled {
                color: #666666;
            }
        """

        # Apply the same font size increase as section titles
        for radio_button in [self.left_eye_radio, self.right_eye_radio]:
            radio_button.setStyleSheet(radio_style)
            font = radio_button.font()
            font.setPointSize(font.pointSize() + 2)  # Increase font size by 2 points like section titles
            radio_button.setFont(font)

        # Add radio buttons to button group for exclusive selection
        self.radio_group.addButton(self.left_eye_radio, 0)  # ID 0 for left eye
        self.radio_group.addButton(self.right_eye_radio, 1)  # ID 1 for right eye

        # Create a container for the radio buttons
        radio_container = QtWidgets.QWidget()
        radio_container.setContentsMargins(0, 0, 0, 0)

        # Create a horizontal layout for the radio buttons
        radio_layout = FramelessHLayout()
        radio_layout.setContentsMargins(0, 0, 0, 0)
        radio_layout.setAlignment(QtCore.Qt.AlignCenter)
        radio_container.setLayout(radio_layout)

        # Add the radio buttons to the layout with increased spacing
        radio_layout.addWidget(self.left_eye_radio)
        radio_layout.addSpacing(40)  # Increased spacing between radio buttons for larger text
        radio_layout.addWidget(self.right_eye_radio)

        # Add the radio container to the main vertical layout
        symmetry_layout.addWidget(radio_container, 0, QtCore.Qt.AlignCenter)

        # Set initial state of the radio buttons based on checkbox state
        self.left_eye_radio.setEnabled(not self.symmetry_checkbox.isChecked())
        self.right_eye_radio.setEnabled(not self.symmetry_checkbox.isChecked())

        # Increase stretch factor to provide more space for the symmetry section
        self.horizontal_layout.addWidget(symmetry_container, 1.5)

    def setup_eyeball_section(self):
        """Set up the eyeball section with size and curvature sliders."""
        eyeball_container = QtWidgets.QWidget()
        # Add border and background style to the container
        eyeball_container.setStyleSheet(
            """
            QWidget {
                border: 2px solid #4A4A4A;
                border-radius: 6px;
                background-color: #2D2D2D;
                padding: 8px;
            }
        """
        )
        eyeball_layout = FramelessVLayout()
        eyeball_layout.setContentsMargins(10, 10, 10, 10)  # Consistent margins for all sections
        eyeball_container.setLayout(eyeball_layout)

        # Section title with standardized styling
        eyeball_title = self.create_section_title("眼球")
        eyeball_layout.addWidget(eyeball_title)

        # Create a horizontal layout to contain sliders and buttons
        sliders_buttons_layout = FramelessHLayout()
        sliders_buttons_layout.setSpacing(10)  # Add some spacing between sliders and buttons
        eyeball_layout.addLayout(sliders_buttons_layout)

        # Create a vertical layout for sliders
        sliders_layout = FramelessVLayout()
        sliders_layout.setSpacing(10)  # Add spacing between sliders

        # Size slider
        size_layout = FramelessHLayout()
        size_label = MStyleMixin.instance_wrapper(MLabel("大小:"))
        size_label.foreground_color(dayu_theme.secondary_text_color)
        # Ensure no border on the label
        size_label.setStyleSheet("border: none; background-color: transparent;")
        self.size_slider = MSlider(QtCore.Qt.Horizontal)
        self.size_slider.setRange(0, 100)
        self.size_slider.setValue(50)
        size_layout.addWidget(size_label)
        size_layout.addWidget(self.size_slider)
        sliders_layout.addLayout(size_layout)

        # Curvature slider
        curvature_layout = FramelessHLayout()
        curvature_label = MStyleMixin.instance_wrapper(MLabel("弧度:"))
        curvature_label.foreground_color(dayu_theme.secondary_text_color)
        # Ensure no border on the label
        curvature_label.setStyleSheet("border: none; background-color: transparent;")
        self.eyeball_curvature_slider = MSlider(QtCore.Qt.Horizontal)
        self.eyeball_curvature_slider.setRange(0, 100)
        self.eyeball_curvature_slider.setValue(50)
        curvature_layout.addWidget(curvature_label)
        curvature_layout.addWidget(self.eyeball_curvature_slider)
        sliders_layout.addLayout(curvature_layout)

        # Add sliders layout to the main horizontal layout
        sliders_buttons_layout.addLayout(sliders_layout, 3)  # Give sliders more space

        # Create a vertical layout for the coordinate widget and its label
        rotate_widget_layout = FramelessVLayout()
        rotate_widget_layout.setSpacing(0)  # Add spacing between label and widget

        # Create a label for the coordinate widget
        rotate_label = MStyleMixin.instance_wrapper(MLabel("旋转面板"))
        rotate_label.foreground_color(dayu_theme.secondary_text_color)
        rotate_label.setAlignment(QtCore.Qt.AlignCenter)
        # Ensure no border on the label
        rotate_label.setStyleSheet("border: none; background-color: transparent;")
        rotate_widget_layout.addWidget(rotate_label)

        # Create coordinate widget
        self.rotate_widget = CoordinateWidget()
        self.rotate_widget.setMinimumSize(100, 100)
        self.rotate_widget.setMaximumSize(100, 100)

        self.rotate_widget.pointMoved.connect(self._on_coordinate_point_moved)
        rotate_widget_layout.addWidget(self.rotate_widget)

        # Add coordinate widget layout to the main horizontal layout
        sliders_buttons_layout.addLayout(rotate_widget_layout, 2)  # Give coordinate widget space

        # Create a vertical layout for buttons
        buttons_layout = FramelessVLayout()
        buttons_layout.setSpacing(10)  # Add spacing between buttons
        buttons_layout.setAlignment(QtCore.Qt.AlignCenter)  # Center the buttons vertically

        # Auto match button
        self.auto_match_button = MStyleMixin.instance_wrapper(MToolButton())
        self.auto_match_button.setText("自动匹配")
        self.auto_match_button.setFixedWidth(120)  # Set fixed width for buttons
        self.auto_match_button.setToolButtonStyle(QtCore.Qt.ToolButtonTextOnly)  # Show text only

        # Apply custom style to match the free mode button
        self.auto_match_button.setStyleSheet(
            """
            QToolButton {
                border: 3px solid #3E3E3E;
                border-radius: 8px;
                background-color: #3A3A3A;
                color: #CCCCCC;
                padding: 3px;
            }
            QToolButton:checked {
                background-color: #1E90FF;  /* Bright blue when checked */
                color: white;
                font-weight: bold;
            }
            QToolButton:hover {
                border-color: #5E5E5E;
                background-color: #4A4A4A;
            }
            QToolButton:pressed {
                background-color: #1A1A1A;
            }
        """
        )
        buttons_layout.addWidget(self.auto_match_button)

        # Free mode button (toggleable)
        self.free_mode_button = MStyleMixin.instance_wrapper(MToolButton())
        self.free_mode_button.setText("自由模式")
        self.free_mode_button.setCheckable(True)  # Make the button toggleable
        self.free_mode_button.setFixedWidth(120)  # Set fixed width for buttons
        self.free_mode_button.setToolButtonStyle(QtCore.Qt.ToolButtonTextOnly)  # Show text only

        # Apply custom style to make the checked state more visible
        self.free_mode_button.setStyleSheet(
            """
            QToolButton {
                border: 3px solid #3E3E3E;
                border-radius: 8px;
                background-color: #3A3A3A;
                color: #CCCCCC;
                padding: 3px;
            }
            QToolButton:checked {
                background-color: #1E90FF;  /* Bright blue when checked */
                color: white;
                font-weight: bold;
            }
            QToolButton:hover {
                border-color: #5E5E5E;
                background-color: #4A4A4A;
            }
            QToolButton:pressed {
                background-color: #1A1A1A;
            }
        """
        )

        buttons_layout.addWidget(self.free_mode_button)

        # Add buttons layout to the main horizontal layout
        sliders_buttons_layout.addLayout(buttons_layout, 1)  # Give buttons less space

        # Add stretch to push all content to the top
        eyeball_layout.addStretch(1)

        # Maintain larger stretch factor for eyeball section
        self.horizontal_layout.addWidget(eyeball_container, 4)

    def setup_cornea_section(self):
        """Set up the cornea section with curvature and scale sliders."""
        cornea_container = QtWidgets.QWidget()
        # Add border and background style to the container
        cornea_container.setStyleSheet(
            """
            QWidget {
                border: 2px solid #4A4A4A;
                border-radius: 6px;
                background-color: #303030;
                padding: 0px 8px 8px 8px;  /* No padding at the top */
            }
        """
        )
        cornea_layout = FramelessVLayout()
        cornea_layout.setContentsMargins(10, 10, 10, 10)
        cornea_container.setLayout(cornea_layout)

        # Section title with standardized styling
        cornea_title = self.create_section_title("角膜")
        cornea_layout.addWidget(cornea_title)

        # Create a widget to contain sliders and make it expand to fill space
        sliders_container_widget = QtWidgets.QWidget()
        sliders_container_widget.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        # Create a horizontal layout for the container widget
        sliders_layout_container = FramelessHLayout()
        sliders_layout_container.setSpacing(10)  # Add some spacing
        sliders_container_widget.setLayout(sliders_layout_container)

        # Add the container widget to the main layout
        cornea_layout.addWidget(sliders_container_widget)

        # Create a vertical layout for sliders
        sliders_layout = FramelessVLayout()
        sliders_layout.setSpacing(10)  # Add spacing between sliders

        # Curvature slider
        curvature_layout = FramelessHLayout()
        curvature_label = MStyleMixin.instance_wrapper(MLabel("弧度:"))
        curvature_label.foreground_color(dayu_theme.secondary_text_color)
        # Ensure no border on the label
        curvature_label.setStyleSheet("border: none; background-color: transparent;")
        self.cornea_curvature_slider = MSlider(QtCore.Qt.Horizontal)
        self.cornea_curvature_slider.setRange(0, 100)
        self.cornea_curvature_slider.setValue(50)
        curvature_layout.addWidget(curvature_label)
        curvature_layout.addWidget(self.cornea_curvature_slider)
        sliders_layout.addLayout(curvature_layout)

        # Scale slider
        scale_layout = FramelessHLayout()
        scale_label = MStyleMixin.instance_wrapper(MLabel("缩放:"))
        scale_label.foreground_color(dayu_theme.secondary_text_color)
        # Ensure no border on the label
        scale_label.setStyleSheet("border: none; background-color: transparent;")
        self.scale_slider = MSlider(QtCore.Qt.Horizontal)
        self.scale_slider.setRange(0, 100)
        self.scale_slider.setValue(50)
        self.scale_slider.setEnabled(False)
        scale_layout.addWidget(scale_label)
        scale_layout.addWidget(self.scale_slider)
        sliders_layout.addLayout(scale_layout)

        # Add sliders layout to the main horizontal layout
        sliders_layout_container.addLayout(sliders_layout, 1)

        # Maintain smaller stretch factor for cornea section
        self.horizontal_layout.addWidget(cornea_container, 2)

    def connect_signals(self):
        """Connect widget signals to slots."""
        self.symmetry_checkbox.stateChanged.connect(self._on_symmetry_changed)
        self.symmetry_checkbox.stateChanged.connect(self._update_eye_selection_state)
        self.radio_group.buttonClicked.connect(self._on_eye_selection_changed)
        self.size_slider.valueChanged.connect(self._on_eyeball_size_changed)
        self.eyeball_curvature_slider.valueChanged.connect(self._on_eyeball_curvature_changed)
        self.cornea_curvature_slider.valueChanged.connect(self._on_cornea_curvature_changed)
        self.scale_slider.valueChanged.connect(self._on_cornea_scale_changed)

        # Connect button signals
        self.auto_match_button.clicked.connect(self._on_auto_match_clicked)
        self.free_mode_button.toggled.connect(self._on_free_mode_clicked)  # Use toggled signal for checkable buttons
        self.reset_button.clicked.connect(self._on_reset_clicked)

    def _on_symmetry_changed(self, state):
        is_symmetric = state == QtCore.Qt.Checked
        self.parametric_eyes.symmetric_mode = is_symmetric
        if is_symmetric:
            self.parametric_eyes.create_symmetric_expressions()
        else:
            self.parametric_eyes.delete_symmetric_expressions()

    def _update_eye_selection_state(self, state):
        """Update the enabled state of the eye selection radio buttons based on checkbox state."""
        is_symmetric = state == QtCore.Qt.Checked
        self.left_eye_radio.setEnabled(not is_symmetric)
        self.right_eye_radio.setEnabled(not is_symmetric)

    def _on_eye_selection_changed(self, button):
        """Handle eye selection changes.

        Args:
            button (QAbstractButton): The radio button that was clicked
        """
        selected_eye = EyeSelection.LEFT if button == self.left_eye_radio else EyeSelection.RIGHT
        self.parametric_eyes.selected_eye = selected_eye
        if self.free_mode_button.isChecked():
            self.parametric_eyes.enter_free_edit_mode()

    def _on_auto_match_clicked(self):
        """Handle auto match button click."""
        self.parametric_eyes.auto_match_eye_drivers()

        # Get the current eye size from the selected eye driver
        eye_size = self.parametric_eyes.eye_size

        # Map the eye size back to slider value (0-100)
        # Reverse the mapping from _on_eyeball_size_changed
        if eye_size <= 1.0:
            # Map from 0.8-1.0 to 0-50
            slider_value = ((eye_size - 0.8) / 0.2) * 50
        else:
            # Map from 1.0-1.7 to 50-100
            slider_value = 50 + ((eye_size - 1.0) / 0.7) * 50

        # Set the slider position
        self.size_slider.setValue(int(slider_value))
        self.eyeball_curvature_slider.setValue(50)  # Reset curvature slider

    def _on_free_mode_clicked(self, checked):
        """Handle free mode button click."""
        if checked:
            self.parametric_eyes.enter_free_edit_mode()
            self.size_slider.setEnabled(False)
            self.eyeball_curvature_slider.setEnabled(False)
            self.rotate_widget.setEnabled(False)  # Disable rotate widget in free mode
            self.rotate_widget.set_point_color("#888888")  # Set control point to gray in free mode
        else:
            self.parametric_eyes.exit_free_edit_mode()
            self.size_slider.setEnabled(True)
            self.eyeball_curvature_slider.setEnabled(True)
            self.rotate_widget.setEnabled(True)  # Re-enable rotate widget when exiting free mode
            self.rotate_widget.set_point_color("#1890FF")  # Restore control point color when exiting free mode

    def _on_eyeball_size_changed(self, value):
        # Map from 0-50 to 0.8-1.0, and from 51-100 to 1.0-1.7
        if value <= 50:
            normalized_value = 0.8 + (value / 50.0) * 0.2
        else:
            normalized_value = 1.0 + ((value - 50) / 50.0) * 0.7
        self.parametric_eyes.set_eyeball_size(normalized_value)

    def _on_eyeball_curvature_changed(self, value):
        normalized_value = 0.01 + (value / 100.0) * 1.99  # Map from 0-100 to 0.01-2.0
        self.parametric_eyes.set_eyeball_curvature(normalized_value)

    def _on_cornea_curvature_changed(self, value):
        # Map from 0-50 to 0.3-1.0, and from 51-100 to 1.0-2.0
        if value <= 50:
            normalized_value = 0.3 + (value / 50.0) * 0.7
        else:
            normalized_value = 1.0 + ((value - 50) / 50.0)
        self.parametric_eyes.set_cornea_curvature(normalized_value)

    def _on_cornea_scale_changed(self, value):
        normalized_value = value / 100.0
        logging.info(f"Cornea scale changed: {normalized_value}")
        # TODO: Implement cornea scale change in parametric_eyes

    def _on_reset_clicked(self):
        """Handle reset button click."""
        self.parametric_eyes.reset_eye_drivers()

        # Reset sliders
        self.size_slider.setValue(50)
        self.eyeball_curvature_slider.setValue(50)
        self.cornea_curvature_slider.setValue(50)

        # Reset coordinate widget
        self.rotate_widget.set_point_position(0, 0)

    def _on_coordinate_point_moved(self, point):
        """Handle coordinate point movement.

        Args:
            point (QPointF): The new position of the control point
        """
        # Normalize coordinates to -1.0 to 1.0 range
        center = self.rotate_widget.get_center()
        normalized_x = 10.0 * point.x() / center.x()
        normalized_y = 10.0 * point.y() / center.y()

        self.parametric_eyes.rotate_eyeball(normalized_x, normalized_y)
