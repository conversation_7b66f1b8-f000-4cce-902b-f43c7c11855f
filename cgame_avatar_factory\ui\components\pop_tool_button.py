# Import third-party modules
import dayu_widgets
from dayu_widgets import <PERSON><PERSON><PERSON><PERSON>utt<PERSON>
from dayu_widgets import dayu_theme
from dayu_widgets.mixin import cursor_mixin
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.ui.components.factor_slider import FactorSlider
from cgame_avatar_factory.ui.mixin.style_mixin import MStyleMixin


@MStyleMixin.cls_wrapper
class RandomMergeToolButton(MToolButton):
    sig_value_changed = QtCore.Signal(float)
    sig_clicked = QtCore.Signal()

    def __init__(self, parent=None):
        super(RandomMergeToolButton, self).__init__(parent)
        self.setup_ui()

    @property
    def slider_value(self):
        return self.slider.value()

    def hide_menu(self):
        self.menu.hide()

    def setup_ui(self):
        self.menu = MStyleMixin.instance_wrapper(dayu_widgets.MMenu(parent=self))
        self.menu.frameless()
        self.menu.setFixedWidth(200)

        slider_label = MStyleMixin.instance_wrapper(dayu_widgets.MLabel("平均融合概率："))
        slider_label.foreground_color(dayu_theme.secondary_text_color).transparent_background()
        slider_label_action = QtWidgets.QWidgetAction(self)
        slider_label_action.setDefaultWidget(slider_label)
        slider_label.setToolTip("该值越大，融合后的脸越平均")

        self.slider = FactorSlider(0.01)
        self.slider.setRange(0, 1)
        slider_action = QtWidgets.QWidgetAction(self)
        slider_action.setDefaultWidget(self.slider)
        self.slider.uniformed_value_changed.connect(self.sig_value_changed)

        self.button = dayu_widgets.MPushButton("开始随机融合").small()
        button_action = QtWidgets.QWidgetAction(self)
        button_action.setDefaultWidget(self.button)
        self.button.clicked.connect(self.sig_clicked)

        self.menu.addAction(slider_label_action)
        self.menu.addAction(slider_action)
        self.menu.addSeparator()
        self.menu.addAction(button_action)
        self.setMenu(self.menu)
        self.setPopupMode(QtWidgets.QToolButton.InstantPopup)


@cursor_mixin
@MStyleMixin.cls_wrapper
class HoverToolButton(QtWidgets.QToolButton):
    pass
