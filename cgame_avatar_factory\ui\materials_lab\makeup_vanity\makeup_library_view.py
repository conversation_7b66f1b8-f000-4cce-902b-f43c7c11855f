"""
Makeup Library View Module

This module defines the MakeupLibraryView class, which displays makeup items in a grid view.
"""

# Import built-in modules
import logging

# Import third-party modules
import dayu_widgets
from dayu_widgets import MItemViewSet
from dayu_widgets import dayu_theme
from qtpy import QtCore
from qtpy import QtGui
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.ui.mixin.style_mixin import MStyleMixin


class MakeupItemDelegate(QtWidgets.QStyledItemDelegate):
    """
    Delegate for rendering makeup items in the view.
    """

    def __init__(self, parent=None):
        super(MakeupItemDelegate, self).__init__(parent)
        self.logger = logging.getLogger(__name__)
        self._spacing = 10
        self._thumbnail_size = 128
        self._text_height = 40

    def paint(self, painter, option, index):
        """
        Paint the makeup item.

        Args:
            painter (QPainter): Painter to use
            option (QStyleOptionViewItem): Style options
            index (QModelIndex): Index of the item to paint
        """
        # Get data from model
        icon = index.data(QtCore.Qt.DecorationRole)
        text = index.data(QtCore.Qt.DisplayRole)

        # Calculate rectangles
        rect = option.rect
        thumbnail_rect = QtCore.QRect(
            rect.x() + (rect.width() - self._thumbnail_size) // 2,
            rect.y() + self._spacing,
            self._thumbnail_size,
            self._thumbnail_size,
        )
        text_rect = QtCore.QRect(
            rect.x() + self._spacing,
            rect.y() + self._thumbnail_size + self._spacing,
            rect.width() - self._spacing * 2,
            self._text_height,
        )

        # Draw selection background if selected
        if option.state & QtWidgets.QStyle.State_Selected:
            painter.fillRect(rect, dayu_theme.primary_color)
        elif option.state & QtWidgets.QStyle.State_MouseOver:
            painter.fillRect(rect, dayu_theme.background_selected_color)

        # Draw thumbnail
        if icon:
            # For SVG icons, we need to use the QIcon's pixmap method
            if isinstance(icon, QtGui.QIcon):
                pixmap = icon.pixmap(self._thumbnail_size, self._thumbnail_size)
                painter.drawPixmap(thumbnail_rect, pixmap)
            else:
                icon.paint(painter, thumbnail_rect)

        # Draw text
        painter.setPen(QtGui.QPen(QtGui.QColor(dayu_theme.secondary_text_color)))
        font = painter.font()
        font.setPointSize(10)
        painter.setFont(font)
        painter.drawText(text_rect, QtCore.Qt.AlignCenter, text)

    def sizeHint(self, option, index):
        """
        Return the size of the item.

        Args:
            option (QStyleOptionViewItem): Style options
            index (QModelIndex): Index of the item

        Returns:
            QSize: Size of the item
        """
        width = self._thumbnail_size + self._spacing * 2
        height = self._thumbnail_size + self._text_height + self._spacing * 3
        return QtCore.QSize(width, height)


class DraggableMixin(object):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setViewMode(QtWidgets.QListView.IconMode)
        self.setAcceptDrops(False)  # Do not accept drops
        # We're implementing custom drag and drop, so we don't use Qt's built-in drag system
        self.setDragEnabled(False)


class MakeupLibraryView(DraggableMixin, dayu_widgets.MBigView):
    """
    View for displaying makeup items in a grid.
    """

    sig_item_clicked = QtCore.Signal(dict)
    sig_context_menu = QtCore.Signal(object, object)

    def __init__(self, parent=None):
        super(MakeupLibraryView, self).__init__(parent)
        self.logger = logging.getLogger(__name__)
        self._drag_start_position = None
        self.setup_ui()

    def setup_ui(self):
        """Set up the UI."""
        # Set view properties
        self.setResizeMode(QtWidgets.QListView.Adjust)
        self.setMovement(QtWidgets.QListView.Static)
        self.setSpacing(10)
        self.setUniformItemSizes(True)

        # Set selection properties
        self.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)

        # Set delegate
        self.setItemDelegate(MakeupItemDelegate(self))

        # Connect signals
        self.clicked.connect(self.slot_item_clicked)
        self.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.slot_context_menu)

    def mousePressEvent(self, event):
        """
        Handle mouse press events.

        Args:
            event (QMouseEvent): Mouse event
        """
        if event.button() == QtCore.Qt.LeftButton:
            self._drag_start_position = event.pos()
        super(MakeupLibraryView, self).mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """
        Handle mouse move events.

        Args:
            event (QMouseEvent): Mouse event
        """
        if not (event.buttons() & QtCore.Qt.LeftButton):
            return

        if not self._drag_start_position:
            return

        # Check if the distance is large enough to start a drag
        distance = (event.pos() - self._drag_start_position).manhattanLength()
        if distance < QtWidgets.QApplication.startDragDistance():
            return

        # Get the item at the drag start position
        index = self.indexAt(self._drag_start_position)
        if not index.isValid():
            return

        # Get original data
        model_index = self.model().index(index.row(), 0)

        # Try to get data from different roles
        item_data = None

        # First try to get complete data from UserRole
        user_role_data = self.model().data(model_index, QtCore.Qt.UserRole)
        if user_role_data:
            item_data = user_role_data
        else:
            # If UserRole has no data, try to get from ItemDataRole
            # Note: dayu_widgets may use custom roles to store complete data
            for role in range(QtCore.Qt.UserRole, QtCore.Qt.UserRole + 10):
                role_data = self.model().data(model_index, role)
                if role_data and isinstance(role_data, dict) and "name" in role_data:
                    item_data = role_data
                    break

        # If still no data found, create basic data using DisplayRole
        if not item_data:
            display_data = self.model().data(model_index, QtCore.Qt.DisplayRole)
            item_data = {"name": display_data}

            # Try to add other possible fields
            for column in range(1, self.model().columnCount()):
                header_data = self.model().headerData(column, QtCore.Qt.Horizontal, QtCore.Qt.DisplayRole)
                if header_data:
                    col_index = self.model().index(index.row(), column)
                    col_data = self.model().data(col_index, QtCore.Qt.DisplayRole)
                    if col_data:
                        item_data[header_data] = col_data

        if not item_data:
            return

        # Prepare drag operation with item data

        # Create mime data
        mime_data = QtCore.QMimeData()

        # Set both text and custom format data
        mime_data.setText(str(item_data))  # Set as text for compatibility
        mime_data.setData("application/x-makeup-item", QtCore.QByteArray(str(item_data).encode()))

        # Create drag
        drag = QtGui.QDrag(self)
        drag.setMimeData(mime_data)

        # Set drag pixmap
        icon = index.data(QtCore.Qt.DecorationRole)
        if icon:
            if isinstance(icon, QtGui.QIcon):
                pixmap = icon.pixmap(64, 64)
            else:
                # Create a colored square as fallback
                pixmap = QtGui.QPixmap(64, 64)
                pixmap.fill(QtGui.QColor("#2683d9"))  # Use app primary color

            drag.setPixmap(pixmap)
            drag.setHotSpot(QtCore.QPoint(pixmap.width() // 2, pixmap.height() // 2))

        # Execute drag with copy action
        drag.exec_(QtCore.Qt.CopyAction)

    def startDrag(self, supported_actions):
        """
        Start drag operation (overridden but not used - we use mouseMoveEvent instead).

        Args:
            supported_actions (Qt.DropActions): Supported drop actions
        """
        # We're using our custom implementation in mouseMoveEvent instead

    @QtCore.Slot(QtCore.QModelIndex)
    def slot_item_clicked(self, index):
        """
        Handle item click.

        Args:
            index (QModelIndex): Index of the clicked item
        """
        # Get original data
        model_index = self.model().index(index.row(), 0)

        # Try to get data from different roles
        item_data = None

        # First try to get complete data from UserRole
        user_role_data = self.model().data(model_index, QtCore.Qt.UserRole)
        if user_role_data:
            item_data = user_role_data
        else:
            # If UserRole has no data, try to get from ItemDataRole
            # Note: dayu_widgets may use custom roles to store complete data
            for role in range(QtCore.Qt.UserRole, QtCore.Qt.UserRole + 10):
                role_data = self.model().data(model_index, role)
                if role_data and isinstance(role_data, dict) and "name" in role_data:
                    item_data = role_data
                    break

        # If still no data found, create basic data using DisplayRole
        if not item_data:
            display_data = self.model().data(model_index, QtCore.Qt.DisplayRole)
            item_data = {"name": display_data}

            # Try to add other possible fields
            for column in range(1, self.model().columnCount()):
                header_data = self.model().headerData(column, QtCore.Qt.Horizontal, QtCore.Qt.DisplayRole)
                if header_data:
                    col_index = self.model().index(index.row(), column)
                    col_data = self.model().data(col_index, QtCore.Qt.DisplayRole)
                    if col_data:
                        item_data[header_data] = col_data

        if item_data:
            self.sig_item_clicked.emit(item_data)

    @QtCore.Slot(QtCore.QPoint)
    def slot_context_menu(self, pos):
        """
        Handle context menu request.

        Args:
            pos (QPoint): Position of the context menu request
        """
        index = self.indexAt(pos)
        if index.isValid():
            # Get original data
            model_index = self.model().index(index.row(), 0)

            # Try to get data from different roles
            item_data = None

            # First try to get complete data from UserRole
            user_role_data = self.model().data(model_index, QtCore.Qt.UserRole)
            if user_role_data:
                item_data = user_role_data
            else:
                # If UserRole has no data, try to get from ItemDataRole
                # Note: dayu_widgets may use custom roles to store complete data
                for role in range(QtCore.Qt.UserRole, QtCore.Qt.UserRole + 10):
                    role_data = self.model().data(model_index, role)
                    if role_data and isinstance(role_data, dict) and "name" in role_data:
                        item_data = role_data
                        break

            # If still no data found, create basic data using DisplayRole
            if not item_data:
                display_data = self.model().data(model_index, QtCore.Qt.DisplayRole)
                item_data = {"name": display_data}

                # Try to add other possible fields
                for column in range(1, self.model().columnCount()):
                    header_data = self.model().headerData(column, QtCore.Qt.Horizontal, QtCore.Qt.DisplayRole)
                    if header_data:
                        col_index = self.model().index(index.row(), column)
                        col_data = self.model().data(col_index, QtCore.Qt.DisplayRole)
                        if col_data:
                            item_data[header_data] = col_data

            self.sig_context_menu.emit(item_data, self.mapToGlobal(pos))


@MStyleMixin.cls_wrapper
class MakeupLibraryViewSet(MItemViewSet):
    """
    View set for the makeup library, including toolbar and view.
    """

    def __init__(self, parent=None):
        super(MakeupLibraryViewSet, self).__init__(view_type=MakeupLibraryView, parent=parent)
        self.tool_bar_visible = False
        self.pagination_visible = False

        # Set view properties
        self.item_view.setViewMode(QtWidgets.QListView.IconMode)
        self.item_view.setResizeMode(QtWidgets.QListView.Adjust)
        self.item_view.setMovement(QtWidgets.QListView.Static)
        self.item_view.setSpacing(10)
        self.item_view.setUniformItemSizes(True)
        self.item_view.setIconSize(QtCore.QSize(128, 128))
        self.item_view.setGridSize(QtCore.QSize(150, 180))

        # Set column headers
        self.set_header_list(
            [
                {
                    "label": "name",
                    "key": "name",
                    "searchable": True,
                    "draggable": True,
                    "icon": lambda x, y: y.get("icon"),
                },
                {
                    "label": "item_path",
                    "key": "item_path",
                    "draggable": True,
                },
                {
                    "label": "category",
                    "key": "category",
                    "draggable": True,
                },
                {
                    "label": "thumbnail_path",
                    "key": "thumbnail_path",
                    "draggable": True,
                },
            ],
        )
