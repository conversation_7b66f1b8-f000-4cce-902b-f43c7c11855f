# Import built-in modules
import logging

# Import third-party modules
from dayu_widgets import dayu_theme
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory import constants as const
from cgame_avatar_factory import core
from cgame_avatar_factory.ui.components.layout import FramelessVLayout
from cgame_avatar_factory.ui.mixin.style_mixin import MStyleMixin


class FloatingPanel(QtWidgets.QWidget):
    """A floating panel widget that can be expanded and collapsed.

    This panel provides a toggle button to show/hide a content panel.
    When expanded, it displays the content panel to the left of the floating panel.

    Attributes:
        sig_expanded_changed: Signal emitted when expanded state changes
    """

    sig_expanded_changed = QtCore.Signal(bool)

    def __init__(self, parent=None):
        """Initialize the floating panel.

        Args:
            parent: Parent widget
        """
        super(FloatingPanel, self).__init__(parent)
        self.logger = logging.getLogger(__name__)

        self._expanded = False
        self._content_panel = None
        self._content_widget = None
        self._floating_panel_pos = None
        self._fade_in_animation = None
        self._fade_out_animation = None

        self.setMinimumHeight(180)

        self._setup_ui()

    def _setup_ui(self):
        """Setup the UI components."""
        self.main_layout = QtWidgets.QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(self.main_layout)
        vertical_text = "\n".join(const.OPEN_EDIT_MODE_LIST)
        self.toggle_button = QtWidgets.QPushButton(vertical_text)

        self.toggle_button.clicked.connect(self.toggle_expanded)

        self.toolbar_container = QtWidgets.QWidget()
        self.toolbar_container.setStyleSheet("background-color: #424242; border-radius: 4px;")
        self.toolbar_container.setFixedWidth(40)

        self.toolbar_layout = QtWidgets.QVBoxLayout()
        self.toolbar_layout.setContentsMargins(0, 5, 0, 5)

        self.toolbar_container.setLayout(self.toolbar_layout)
        self.toolbar_layout.addWidget(self.toggle_button)

        self.main_layout.addWidget(self.toolbar_container)

        self._content_panel = MStyleMixin.cls_wrapper(QtWidgets.QWidget)()
        self._content_panel.setSizePolicy(
            QtWidgets.QSizePolicy.Expanding,
            QtWidgets.QSizePolicy.Expanding,
        )
        self._content_panel.frameless().border_radius(8).background_color(
            dayu_theme.background_in_color,
        )
        shadow = QtWidgets.QGraphicsDropShadowEffect(self._content_panel)
        shadow.setBlurRadius(15)
        self._content_panel.setGraphicsEffect(shadow)

        self._content_layout = FramelessVLayout()
        self._content_layout.setContentsMargins(15, 10, 15, 15)

        self._content_panel.setLayout(self._content_layout)

        self._content_panel.hide()

    def set_content_widget(self, widget):
        """Set the content widget to be displayed in the content panel.

        Args:
            widget: Widget to be displayed in the content panel
        """
        if self._content_widget:
            self._content_layout.removeWidget(self._content_widget)
            self._content_widget.setParent(None)

        self._content_widget = widget
        if widget:
            self._content_layout.addWidget(widget)

    def toggle_expanded(self):
        """Toggle the expanded state of the floating panel.

        When expanded, the content panel is shown to the left of the floating panel.
        When collapsed, the content panel is hidden.
        """
        with core.get_reporter_instance() as api:
            api.report_count(
                event_name="main-call",
                action="edit mode function call",
                tool_name=const.PACKAGE_NAME,
            )
        self._expanded = not self._expanded

        if self._expanded:
            parent = self.parentWidget()
            if not parent:
                self.logger.warning("无法获取父组件，取消展开面板")
                return

            if self._content_panel.parent() != parent:
                self._content_panel.setParent(parent)
            self._floating_panel_pos = self.pos()

            self._center_panel_in_parent()
            self._content_panel.show()

            QtCore.QTimer.singleShot(10, self._ensure_panels_visible)
            close_text = "\n".join(const.OFF_EDIT_MODE_LIST)
            self.toggle_button.setText(close_text)

            if self._content_widget and hasattr(self._content_widget, "_refresh_targets_list"):
                QtCore.QTimer.singleShot(100, self._content_widget._refresh_targets_list)
        else:
            # Close all blendshape targets that are being edited when exiting edit mode
            if self._content_widget and hasattr(self._content_widget, "close_all_editing_targets"):
                self._content_widget.close_all_editing_targets()
                self.logger.info("关闭编辑模式面板时关闭所有编辑目标")

            self._fade_out_animation = QtCore.QPropertyAnimation(self._content_panel, b"windowOpacity")
            self._fade_out_animation.setDuration(100)
            self._fade_out_animation.setStartValue(1.0)
            self._fade_out_animation.setEndValue(0.0)
            self._fade_out_animation.finished.connect(self._content_panel.hide)
            self._fade_out_animation.start()

            open_text = "\n".join(const.OPEN_EDIT_MODE_LIST)
            self.toggle_button.setText(open_text)

            self.raise_()

        self.sig_expanded_changed.emit(self._expanded)

    def _center_panel_in_parent(self):
        """Position the content panel to the left of the floating panel.

        Calculates the appropriate position for the content panel based on the
        parent widget's size and the floating panel's position.
        """

        # Get parent component. Note: This function design is not ideal, should be optimized in the future
        parent = self.parentWidget()
        if not self._content_panel or not parent:
            self.logger.warning("无法定位面板：面板或父组件不存在")
            return

        if self._content_panel.parent() != parent:
            self._content_panel.setParent(parent)
            self.logger.info(f"重新设置内容面板父组件: {parent.objectName()}")

        parent_rect = parent.rect()
        self.logger.debug(f"父窗口尺寸: {parent_rect.width()}x{parent_rect.height()}")

        panel_width = int(parent_rect.width() * 0.35)
        panel_height = int(parent_rect.height() * 0.35)
        self.logger.debug(f"计算的面板尺寸: {panel_width}x{panel_height}")

        self._content_panel.resize(panel_width, panel_height)

        floating_panel_pos = self.pos()
        floating_panel_height = self.height()

        x = floating_panel_pos.x() - panel_width - 200
        y = floating_panel_pos.y() + floating_panel_height - panel_height

        if x < 0:
            x = 60
        if y < 0:
            y = 0

        if y + panel_height > parent_rect.height():
            y = parent_rect.height() - panel_height - 5

        self._content_panel.move(x, y)

    def moveEvent(self, event):
        """Handle move events to reposition the content panel.

        Args:
            event: Move event
        """
        super(FloatingPanel, self).moveEvent(event)
        if self._expanded and self._content_panel and self._content_panel.isVisible():
            self._center_panel_in_parent()

    def parentWidget(self):
        """Get the parent widget.

        Returns:
            QWidget: Parent widget or None if no parent
        """
        parent = super(FloatingPanel, self).parentWidget()
        if not parent:
            return None

        return parent

    def _ensure_panels_visible(self):
        """Ensure both panels are visible and correctly stacked."""
        if self._content_panel and self._content_panel.isVisible():
            self._content_panel.raise_()
            self.raise_()
            self.logger.info("确保面板可见并正确叠放")

    def closeEvent(self, event):
        """Handle close events to also close the content panel.

        Args:
            event: Close event
        """
        if self._content_panel:
            self._content_panel.close()
        super(FloatingPanel, self).closeEvent(event)
