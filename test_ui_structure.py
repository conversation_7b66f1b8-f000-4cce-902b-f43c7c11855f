#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试UI结构的脚本
用于验证毛发工作室UI组件的基本结构和导入
"""

import sys
import os

# 添加项目路径到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_ui_structure():
    """测试UI结构"""
    print("开始测试毛发工作室UI结构...")
    
    # 测试1: 检查文件是否存在
    ui_files = [
        "cgame_avatar_factory/hair_studio/ui/hair_studio_tab.py",
        "cgame_avatar_factory/hair_studio/ui/base_hair_tab.py", 
        "cgame_avatar_factory/hair_studio/ui/asset_library/asset_library.py",
        "cgame_avatar_factory/hair_studio/ui/component_list.py",
        "cgame_avatar_factory/hair_studio/ui/editor_area.py"
    ]
    
    print("\n1. 检查UI文件是否存在:")
    for file_path in ui_files:
        full_path = os.path.join(project_root, file_path)
        exists = os.path.exists(full_path)
        status = "✓" if exists else "✗"
        print(f"   {status} {file_path}")
    
    # 测试2: 检查UI组件的基本结构
    print("\n2. 检查UI组件结构:")
    
    # 检查毛发素材库的标题修改
    asset_library_path = os.path.join(project_root, "cgame_avatar_factory/hair_studio/ui/asset_library/asset_library.py")
    if os.path.exists(asset_library_path):
        with open(asset_library_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        checks = [
            ("毛发素材库标题", "毛发素材库" in content),
            ("设置按钮", "settings_btn" in content),
            ("设置按钮图标", "settings_line.svg" in content),
            ("搜索框中文提示", "搜索🔍" in content),
            ("设置按钮点击处理", "_on_settings_clicked" in content)
        ]
        
        for check_name, result in checks:
            status = "✓" if result else "✗"
            print(f"   {status} {check_name}")
    
    # 检查组件列表的标题修改
    component_list_path = os.path.join(project_root, "cgame_avatar_factory/hair_studio/ui/component_list.py")
    if os.path.exists(component_list_path):
        with open(component_list_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        checks = [
            ("组件列表标题", "毛发组件列表" in content)
        ]
        
        for check_name, result in checks:
            status = "✓" if result else "✗"
            print(f"   {status} {check_name}")
    
    # 检查编辑区域的标题修改
    editor_area_path = os.path.join(project_root, "cgame_avatar_factory/hair_studio/ui/editor_area.py")
    if os.path.exists(editor_area_path):
        with open(editor_area_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        checks = [
            ("编辑区域标题", "毛发编辑区" in content),
            ("未选择组件提示", "未选择组件" in content)
        ]
        
        for check_name, result in checks:
            status = "✓" if result else "✗"
            print(f"   {status} {check_name}")
    
    print("\n3. UI改进总结:")
    print("   ✓ 毛发素材库标题已改为中文")
    print("   ✓ 添加了右上角设置按钮（齿轮图标）")
    print("   ✓ 改进了搜索框布局，添加了搜索图标")
    print("   ✓ 组件列表标题已改为中文")
    print("   ✓ 编辑区域标题已改为中文")
    print("   ✓ 未选择状态的提示文本已改为中文")
    
    print("\n4. 下一步验证:")
    print("   - 在Maya环境中启动应用程序")
    print("   - 检查毛发工作室tab的UI显示效果")
    print("   - 测试设置按钮的点击功能")
    print("   - 验证搜索功能是否正常工作")
    print("   - 确认素材网格布局显示正确")

if __name__ == "__main__":
    test_ui_structure()
