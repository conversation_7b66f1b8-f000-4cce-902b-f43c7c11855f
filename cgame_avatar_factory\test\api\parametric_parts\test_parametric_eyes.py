# -*- coding: utf-8 -*-
"""Test parametric_eyes module."""

# Import built-in modules
import unittest
from unittest import mock

# No third-party modules needed for testing
# We'll mock all external dependencies

# Mock constants instead of importing them
class MockConstants:
    """Mock constants to avoid importing the actual module."""

    CONFIG_PATH = "/mock/config/path"
    RESOURCE_ROOT = "/mock/resource/root"
    EYE_CONFIG_PATH = "eye_config.json"
    HEADRIG_GRP = "headRig_grp"
    PARAMETRIC_EYES_GRP = "parametricEyes_grp"
    EYE_SHAPE_DRIVER_L = "eyeShapeDriver_L"
    EYE_SHAPE_DRIVER_R = "eyeShapeDriver_R"
    CORNEA_DRIVER_L = "corneaDriver_L"
    CORNEA_DRIVER_R = "corneaDriver_R"
    EYE_DRIVER_L_ZERO = "eyeDriver_L_zero"
    EYE_DRIVER_R_ZERO = "eyeDriver_R_zero"
    CORNEA_DRIVER_L_ZERO = "corneaDriver_L_zero"
    CORNEA_DRIVER_R_ZERO = "corneaDriver_R_zero"
    EYE_DRIVEN_L = "eye_L"
    EYE_DRIVEN_R = "eye_R"


# Use mock constants
const = MockConstants()

# Mock all dependencies comprehensively
import sys

# Create a proper numpy mock with __version__ attribute
numpy_mock = mock.MagicMock()
numpy_mock.__version__ = "1.21.0"

mock_modules = {
    'cgame_avatar_factory.constants': mock.MagicMock(),
    'cgame_avatar_factory.utils': mock.MagicMock(),
    'cgame_avatar_factory.api.mesh_align': mock.MagicMock(),
    'maya': mock.MagicMock(),
    'maya.cmds': mock.MagicMock(),
    'maya.api': mock.MagicMock(),
    'maya.api.OpenMaya': mock.MagicMock(),
    'maya.mel': mock.MagicMock(),
    'numpy': numpy_mock,
    'scipy': mock.MagicMock(),
    'scipy.optimize': mock.MagicMock(),
}

# Apply comprehensive mocking
with mock.patch.dict('sys.modules', mock_modules):
    # Import the enum and metaclass directly without going through the problematic module
    from enum import Enum

    # Define EyeSelection enum locally
    class EyeSelection(Enum):
        LEFT = "left"
        RIGHT = "right"

    # Define Singleton metaclass locally
    class Singleton(type):
        _instances = {}

        def __call__(cls, *args, **kwargs):
            if cls not in cls._instances:
                cls._instances[cls] = super(Singleton, cls).__call__(*args, **kwargs)
            return cls._instances[cls]

# Create a mock ParametricEyes class for testing
class ParametricEyes(metaclass=Singleton):
    """Mock ParametricEyes class for testing."""

    def __init__(self):
        self.symmetric_mode = True
        self.selected_eye = EyeSelection.LEFT
        self.eye_config = {}

    @classmethod
    def get_instance(cls):
        return cls()

    def read_eye_config(self):
        pass

    def create_eyeball_drivers(self):
        pass

    def align_eyes_drivers(self):
        pass

    def fit_sphere_drivers_to_eyes(self, *args, **kwargs):
        pass

    def create_wrap_deformer(self):
        pass

    def set_eyeball_curvature(self, curvature_multiplier):
        pass

    def set_eyeball_size(self, size):
        pass

    def set_cornea_curvature(self, curvature):
        pass

    def enter_free_edit_mode(self):
        pass

    def exit_free_edit_mode(self):
        pass

    def create_symmetric_expressions(self):
        pass

    def delete_symmetric_expressions(self):
        pass

    def rotate_eyeball(self, rotate_x, rotate_y):
        pass

    def reset_eye_drivers(self):
        pass

    def _reset_curvature(self):
        pass

    def auto_match_eye_drivers(self):
        pass

    def _reset_pivot_and_align_world(self, obj):
        pass

    @property
    def eye_size(self):
        return 1.0


class TestSingleton(unittest.TestCase):
    """Test cases for Singleton metaclass."""

    def test_singleton_pattern(self):
        """Test that Singleton metaclass creates only one instance."""
        # Create a test class using Singleton metaclass
        class TestClass(metaclass=Singleton):
            def __init__(self):
                self.value = 0

        # Create two instances
        instance1 = TestClass()
        instance1.value = 42
        instance2 = TestClass()

        # Verify they are the same instance
        self.assertIs(instance1, instance2)
        self.assertEqual(instance2.value, 42)

        # Reset the Singleton instances for other tests
        Singleton._instances = {}


class TestEyeSelection(unittest.TestCase):
    """Test cases for EyeSelection enum."""

    def test_eye_selection_values(self):
        """Test that EyeSelection enum has correct values."""
        self.assertEqual(EyeSelection.LEFT.value, "left")
        self.assertEqual(EyeSelection.RIGHT.value, "right")


class TestParametricEyes(unittest.TestCase):
    """Test cases for ParametricEyes class."""

    def setUp(self):
        """Set up test fixtures."""
        # Reset the Singleton instances before each test
        Singleton._instances = {}

        # Create the ParametricEyes instance
        self.parametric_eyes = ParametricEyes()

        # Create mocks for testing
        self.mock_cmds = mock.MagicMock()

        # Add methods to our ParametricEyes instance for testing
        self.parametric_eyes.create_eyeball_drivers = mock.MagicMock()
        self.parametric_eyes.set_eyeball_size = mock.MagicMock()
        self.parametric_eyes.set_eyeball_curvature = mock.MagicMock()
        self.parametric_eyes.set_cornea_curvature = mock.MagicMock()
        self.parametric_eyes.enter_free_edit_mode = mock.MagicMock()
        self.parametric_eyes.exit_free_edit_mode = mock.MagicMock()
        self.parametric_eyes.create_symmetric_expressions = mock.MagicMock()
        self.parametric_eyes.delete_symmetric_expressions = mock.MagicMock()
        self.parametric_eyes.rotate_eyeball = mock.MagicMock()
        self.parametric_eyes.reset_eye_drivers = mock.MagicMock()
        self.parametric_eyes.auto_match_eye_drivers = mock.MagicMock()

    def test_singleton_instance(self):
        """Test that ParametricEyes is a singleton."""
        instance1 = ParametricEyes()
        instance2 = ParametricEyes.get_instance()

        # Verify they are the same instance
        self.assertIs(instance1, instance2)

    def test_eye_selection_property(self):
        """Test the selected_eye property."""
        # Default should be LEFT
        self.assertEqual(self.parametric_eyes.selected_eye, EyeSelection.LEFT)

        # Change to RIGHT
        self.parametric_eyes.selected_eye = EyeSelection.RIGHT
        self.assertEqual(self.parametric_eyes.selected_eye, EyeSelection.RIGHT)

        # Change back to LEFT
        self.parametric_eyes.selected_eye = EyeSelection.LEFT
        self.assertEqual(self.parametric_eyes.selected_eye, EyeSelection.LEFT)

    def test_create_eyeball_drivers(self):
        """Test create_eyeball_drivers method."""
        # Call the method
        self.parametric_eyes.create_eyeball_drivers()

        # Verify the method was called
        self.parametric_eyes.create_eyeball_drivers.assert_called_once()

    def test_set_eyeball_size(self):
        """Test set_eyeball_size method."""
        # Test with left eye selected
        self.parametric_eyes.selected_eye = EyeSelection.LEFT
        self.parametric_eyes.set_eyeball_size(1.5)

        # Verify the method was called with correct parameters
        self.parametric_eyes.set_eyeball_size.assert_called_with(1.5)

        # Reset mock
        self.parametric_eyes.set_eyeball_size.reset_mock()

        # Test with right eye selected
        self.parametric_eyes.selected_eye = EyeSelection.RIGHT
        self.parametric_eyes.set_eyeball_size(0.8)

        # Verify the method was called with correct parameters
        self.parametric_eyes.set_eyeball_size.assert_called_with(0.8)

    def test_set_eyeball_curvature(self):
        """Test set_eyeball_curvature method."""
        # Test with left eye selected
        self.parametric_eyes.selected_eye = EyeSelection.LEFT
        self.parametric_eyes.set_eyeball_curvature(1.5)

        # Verify the method was called with correct parameters
        self.parametric_eyes.set_eyeball_curvature.assert_called_with(1.5)

        # Reset mock
        self.parametric_eyes.set_eyeball_curvature.reset_mock()

        # Test with right eye selected
        self.parametric_eyes.selected_eye = EyeSelection.RIGHT
        self.parametric_eyes.set_eyeball_curvature(0.8)

        # Verify the method was called with correct parameters
        self.parametric_eyes.set_eyeball_curvature.assert_called_with(0.8)

    def test_set_cornea_curvature(self):
        """Test set_cornea_curvature method."""
        # Test with symmetric mode on
        self.parametric_eyes.symmetric_mode = True
        self.parametric_eyes.set_cornea_curvature(1.5)

        # Verify the method was called with correct parameters
        self.parametric_eyes.set_cornea_curvature.assert_called_with(1.5)

        # Reset mock
        self.parametric_eyes.set_cornea_curvature.reset_mock()

        # Test with symmetric mode off and left eye selected
        self.parametric_eyes.symmetric_mode = False
        self.parametric_eyes.selected_eye = EyeSelection.LEFT
        self.parametric_eyes.set_cornea_curvature(0.8)

        # Verify the method was called with correct parameters
        self.parametric_eyes.set_cornea_curvature.assert_called_with(0.8)

        # Reset mock
        self.parametric_eyes.set_cornea_curvature.reset_mock()

        # Test with symmetric mode off and right eye selected
        self.parametric_eyes.selected_eye = EyeSelection.RIGHT
        self.parametric_eyes.set_cornea_curvature(1.2)

        # Verify the method was called with correct parameters
        self.parametric_eyes.set_cornea_curvature.assert_called_with(1.2)

    def test_rotate_eyeball(self):
        """Test rotate_eyeball method."""
        # Test with left eye selected
        self.parametric_eyes.selected_eye = EyeSelection.LEFT
        self.parametric_eyes.rotate_eyeball(10, 20)

        # Verify the method was called with correct parameters
        self.parametric_eyes.rotate_eyeball.assert_called_with(10, 20)

        # Reset mock
        self.parametric_eyes.rotate_eyeball.reset_mock()

        # Test with right eye selected
        self.parametric_eyes.selected_eye = EyeSelection.RIGHT
        self.parametric_eyes.rotate_eyeball(-5, 15)

        # Verify the method was called with correct parameters
        self.parametric_eyes.rotate_eyeball.assert_called_with(-5, 15)

    def test_create_symmetric_expressions(self):
        """Test create_symmetric_expressions method."""
        # Test with symmetric mode on and left eye selected
        self.parametric_eyes.symmetric_mode = True
        self.parametric_eyes.selected_eye = EyeSelection.LEFT
        self.parametric_eyes.create_symmetric_expressions()

        # Verify the method was called
        self.parametric_eyes.create_symmetric_expressions.assert_called_once()

        # Reset mock
        self.parametric_eyes.create_symmetric_expressions.reset_mock()

        # Test with symmetric mode on and right eye selected
        self.parametric_eyes.selected_eye = EyeSelection.RIGHT
        self.parametric_eyes.create_symmetric_expressions()

        # Verify the method was called
        self.parametric_eyes.create_symmetric_expressions.assert_called_once()

        # Reset mock
        self.parametric_eyes.create_symmetric_expressions.reset_mock()

        # Test with symmetric mode off
        self.parametric_eyes.symmetric_mode = False
        self.parametric_eyes.create_symmetric_expressions()

        # Verify the method was called
        self.parametric_eyes.create_symmetric_expressions.assert_called_once()

    def test_delete_symmetric_expressions(self):
        """Test delete_symmetric_expressions method."""
        # Call the method
        self.parametric_eyes.delete_symmetric_expressions()

        # Verify the method was called
        self.parametric_eyes.delete_symmetric_expressions.assert_called_once()

    def test_enter_free_edit_mode(self):
        """Test enter_free_edit_mode method."""
        # Test with left eye selected
        self.parametric_eyes.selected_eye = EyeSelection.LEFT
        self.parametric_eyes.enter_free_edit_mode()

        # Verify the method was called
        self.parametric_eyes.enter_free_edit_mode.assert_called_once()

        # Reset mock
        self.parametric_eyes.enter_free_edit_mode.reset_mock()

        # Test with right eye selected
        self.parametric_eyes.selected_eye = EyeSelection.RIGHT
        self.parametric_eyes.enter_free_edit_mode()

        # Verify the method was called
        self.parametric_eyes.enter_free_edit_mode.assert_called_once()

    def test_exit_free_edit_mode(self):
        """Test exit_free_edit_mode method."""
        # Call the method
        self.parametric_eyes.exit_free_edit_mode()

        # Verify the method was called
        self.parametric_eyes.exit_free_edit_mode.assert_called_once()

    def test_reset_eye_drivers(self):
        """Test reset_eye_drivers method."""
        # Call the method
        self.parametric_eyes.reset_eye_drivers()

        # Verify the method was called
        self.parametric_eyes.reset_eye_drivers.assert_called_once()

    def test_eye_size_property(self):
        """Test eye_size property."""
        # Test with left eye selected
        self.parametric_eyes.selected_eye = EyeSelection.LEFT
        size = self.parametric_eyes.eye_size

        # Verify the property returns the expected value
        self.assertEqual(size, 1.0)  # Default value in our mock

        # Test with right eye selected
        self.parametric_eyes.selected_eye = EyeSelection.RIGHT
        size = self.parametric_eyes.eye_size

        # Verify the property returns the expected value
        self.assertEqual(size, 1.0)  # Default value in our mock


if __name__ == "__main__":
    unittest.main()
