# Import third-party modules
import dayu_widgets
from dayu_widgets import MTabWidget
from dayu_widgets import dayu_theme
from dayu_widgets.mixin import cursor_mixin
from qtpy import QtCore
from qtpy import QtWidgets


@cursor_mixin
class DNATabBar(QtWidgets.QTabBar):
    def __init__(self, parent=None):
        super(DNATabBar, self).__init__(parent=parent)
        self.setDrawBase(False)

    def tabSizeHint(self, index):
        tab_text = self.tabText(index)
        if self.tabsClosable():
            return QtCore.QSize(
                self.fontMetrics().height() + 20,
                self.fontMetrics().width(tab_text) + 70,
            )
        else:
            return QtCore.QSize(
                self.fontMetrics().height() + 20,
                self.fontMetrics().width(tab_text) + 50,
            )


class DNATabWidget(MTabWidget):
    def __init__(self, parent=None):
        super(DNATabWidget, self).__init__(parent=parent)
        self.bar = DNATabBar()
        self.setTabBar(self.bar)
        self.setTabPosition(MTabWidget.West)
        tab_qss = f"""
        DNATabWidget::pane {{
            border: 0;
        }}
        DNATabBar::tab {{
            border: none;
        }}
        DNATabBar::tab:selected {{
            font-weight: bold;
            border: 3px solid {dayu_theme.primary_color};
            border-left: none;
            border-top: none;
            border-bottom: none;
            border-radius: none;
        }}
        """
        self.setStyleSheet(tab_qss)

    def disable_animation(self):
        self.currentChanged.disconnect(self._play_anim)


class MMenuTabWidget(dayu_widgets.MMenuTabWidget):
    def __init__(self, orientation=QtCore.Qt.Vertical, parent=None):
        super().__init__(orientation, parent=parent)
        tab_qss = f"""
        #bar_widget {{
            background-color: none;
        }}
        MBlockButton:checked{{
            font-weight: bold;
            border: 3px solid {dayu_theme.primary_color};
            border-left: none;
            border-top: none;
            border-bottom: none;
            border-radius: none;
        }}
        """
        self.setStyleSheet(tab_qss)
