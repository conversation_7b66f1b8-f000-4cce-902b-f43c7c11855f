"""Hair Manager Module.

This module provides the HairManager class which serves as the central point for
managing hair assets, components, and their interactions in the Hair Studio tool.
"""

from __future__ import absolute_import

# Import standard library
import os
import json
import logging
import uuid
from collections import OrderedDict

# Import third-party modules
from qtpy import QtCore

# Import local modules
from cgame_avatar_factory.hair_studio.data.models import (
    HairAsset,
    HairComponent,
    HairProject,
)


class HairManager(QtCore.QObject):
    """Manager class for hair studio operations.

    This class serves as the central point for managing hair assets, components,
    and their interactions. It handles data loading, saving, and communication
    between different parts of the application.
    """

    # Signal emitted when assets are loaded
    assets_loaded = QtCore.Signal(list)

    # Signal emitted when components are updated
    components_updated = QtCore.Signal(list)

    # Signal emitted when the selected component changes
    component_selected = QtCore.Signal(object)  # HairComponent or None

    def __init__(self, parent=None):
        """Initialize the HairManager.

        Args:
            parent: Parent QObject
        """
        super(<PERSON><PERSON>ana<PERSON>, self).__init__(parent)

        # Initialize logger
        self._logger = logging.getLogger(__name__)
        self._logger.setLevel(logging.DEBUG)

        # Initialize data structures
        self._assets = OrderedDict()  # asset_id -> HairAsset
        self._components = OrderedDict()  # component_id -> HairComponent
        self._current_project = HairProject()
        self._selected_component_id = None

        # Initialize with sample data for testing
        self._initialize_sample_data()

    def _initialize_sample_data(self):
        """Initialize with sample data for testing purposes."""
        # Sample assets
        sample_assets = [
            {
                "id": "card_1",
                "name": "Hair Card 1",
                "asset_type": "card",
                "thumbnail": None,
                "metadata": {"category": "front_bangs"},
            },
            {
                "id": "xgen_1",
                "name": "XGen Hair 1",
                "asset_type": "xgen",
                "thumbnail": None,
                "metadata": {"strand_count": 1000},
            },
            {
                "id": "curve_1",
                "name": "Hair Curve 1",
                "asset_type": "curve",
                "thumbnail": None,
                "metadata": {"curve_type": "bezier"},
            },
        ]

        # Add sample assets
        for asset_data in sample_assets:
            asset = HairAsset(**asset_data)
            self._assets[asset.id] = asset

    def get_assets(self, asset_type=None):
        """Get a list of all assets, optionally filtered by type.

        Args:
            asset_type (str, optional): Type of assets to filter by.
                If None, returns all assets.

        Returns:
            list: List of HairAsset objects
        """
        if asset_type is None:
            return list(self._assets.values())
        return [asset for asset in self._assets.values() if asset.type == asset_type]

    def get_components(self):
        """Get a list of all components.

        Returns:
            list: List of HairComponent objects
        """
        return list(self._components.values())

    def get_component(self, component_id):
        """Get a component by its ID.

        Args:
            component_id (str): ID of the component to retrieve

        Returns:
            HairComponent or None: The component with the given ID, or None if not found
        """
        return self._components.get(component_id)

    def get_selected_component(self):
        """Get the currently selected component.

        Returns:
            HairComponent or None: The selected component, or None if none is selected
        """
        if self._selected_component_id is None:
            return None
        return self.get_component(self._selected_component_id)

    def select_component(self, component_id):
        """Select a component by its ID.

        Args:
            component_id (str or None): ID of the component to select, or None to clear selection
        """
        if component_id is not None and component_id not in self._components:
            self._logger.warning("Component %s not found", component_id)
            return

        self._selected_component_id = component_id
        self.component_selected.emit(self.get_selected_component())

    def create_component(self, asset_id, **kwargs):
        """Create a new component from an asset.

        Args:
            asset_id (str): ID of the asset to create a component from
            **kwargs: Additional attributes to set on the component

        Returns:
            HairComponent: The created component, or None if creation failed
        """
        if asset_id not in self._assets:
            self._logger.error("Asset %s not found", asset_id)
            return None

        # Create a unique ID for the component
        component_id = str(uuid.uuid4())

        # Create the component
        asset = self._assets[asset_id]
        component = HairComponent(
            id=component_id,
            asset_id=asset_id,
            name=f"{asset.name} ({len(self._components) + 1})",
            type=asset.type,
            **kwargs,
        )

        # Add to our collection
        self._components[component_id] = component
        self._current_project.components.append(component)

        # Select the new component
        self.select_component(component_id)

        # Notify listeners
        self.components_updated.emit(self.get_components())

        return component

    def delete_component(self, component_id):
        """Delete a component by its ID.

        Args:
            component_id (str): ID of the component to delete

        Returns:
            bool: True if the component was deleted, False otherwise
        """
        if component_id not in self._components:
            self._logger.warning("Component %s not found", component_id)
            return False

        # If the deleted component is selected, clear the selection
        if self._selected_component_id == component_id:
            self.select_component(None)

        # Remove from our collections
        component = self._components.pop(component_id)
        if component in self._current_project.components:
            self._current_project.components.remove(component)

        # TODO: Clean up Maya nodes and other resources

        # Notify listeners
        self.components_updated.emit(self.get_components())

        return True

    def update_component(self, component_id, **kwargs):
        """Update a component's properties.

        Args:
            component_id (str): ID of the component to update
            **kwargs: Attributes to update on the component

        Returns:
            bool: True if the component was updated, False otherwise
        """
        if component_id not in self._components:
            self._logger.warning("Component %s not found", component_id)
            return False

        component = self._components[component_id]

        # Update the component's attributes
        for key, value in kwargs.items():
            if hasattr(component, key):
                setattr(component, key, value)
            else:
                self._logger.warning("Component has no attribute %s", key)

        # Notify listeners
        self.components_updated.emit(self.get_components())

        # If this is the selected component, emit selection changed
        if component_id == self._selected_component_id:
            self.component_selected.emit(component)

        return True

    def save_project(self, filepath):
        """Save the current project to a file.

        Args:
            filepath (str): Path to save the project to

        Returns:
            bool: True if the project was saved successfully, False otherwise
        """
        try:
            # Prepare data for serialization
            project_data = {
                "version": self._current_project.version,
                "metadata": self._current_project.metadata,
                "components": [
                    {
                        "id": comp.id,
                        "asset_id": comp.asset_id,
                        "name": comp.name,
                        "type": comp.type,
                        "parameters": comp.parameters,
                        "transform": comp.transform,
                        "metadata": comp.metadata,
                    }
                    for comp in self._current_project.components
                ],
            }

            # Write to file
            with open(filepath, "w") as f:
                json.dump(project_data, f, indent=4)

            self._logger.info("Project saved to %s", filepath)
            return True

        except Exception as e:
            self._logger.error("Failed to save project: %s", str(e), exc_info=True)
            return False

    def load_project(self, filepath):
        """Load a project from a file.

        Args:
            filepath (str): Path to the project file to load

        Returns:
            bool: True if the project was loaded successfully, False otherwise
        """
        try:
            # Read from file
            with open(filepath, "r") as f:
                project_data = json.load(f)

            # Clear current state
            self._components.clear()
            self._current_project = HairProject()
            self._selected_component_id = None

            # Update project metadata
            self._current_project.version = project_data.get("version", "1.0")
            self._current_project.metadata = project_data.get("metadata", {})

            # Create components
            for comp_data in project_data.get("components", []):
                component = HairComponent(
                    id=comp_data.get("id", str(uuid.uuid4())),
                    asset_id=comp_data["asset_id"],
                    name=comp_data.get("name", "Unnamed Component"),
                    type=comp_data.get("type", "card"),
                    parameters=comp_data.get("parameters", {}),
                    transform=comp_data.get("transform", {}),
                    metadata=comp_data.get("metadata", {}),
                )
                self._components[component.id] = component
                self._current_project.components.append(component)

            # Notify listeners
            self.components_updated.emit(self.get_components())

            self._logger.info("Project loaded from %s", filepath)
            return True

        except Exception as e:
            self._logger.error("Failed to load project: %s", str(e), exc_info=True)
            return False
