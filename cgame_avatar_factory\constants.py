# Import built-in modules
from enum import Enum
import os

# Import local modules
from cgame_avatar_factory.config import get_config

# Name of current package.
PACKAGE_NAME = "cgame_avatar_factory"
APP_NAME = "[和平精英] 角色工厂"
APP_TITLE_TEMPLATE = "{app_name} - {app_version}"
APP_VERSION = os.getenv("REZ_CGAME_AVATAR_FACTORY_VERSION", "local.dev")
RESOURCE_ROOT = os.getenv("CGAME_AVATAR_FACTORY_RESOURCE", os.path.dirname(__file__))
# Banner button config
BANNER_BUTTON = {
    "wecom": {"username": "pillarchen"},
    "info": {
        "url": "https://doc.weixin.qq.com/doc/w3_AQ0AAwbnALM9wtf207iTR0Neycfo0?scode=AJEAIQdfAAoemH15KBAQ0AAwbnALM",
    },
}

DX11_MATERIAL_NAME = {
    "head": "dx11_shd_head",
    "eyeRight": "dx11_shd_eye",
    "eyeLeft": "dx11_shd_eye",
    "teeth": "dx11_shd_teeth",
    "eyeshell": "Transparent",
    "eyeEdge": "Transparent",
}

RL4 = "rl4Embedded"

PROJECT_ABBR = os.getenv("THM_PROJECT_ABBR")
ZBRUSH_ROOT = os.getenv("THM_ZBRUSH_ROOT")

# Avatar library paths
# Access to the public library is only granted when the project's character complies with MetaHuman specifications.
PUBLIC_AVATAR_FACTORY_PATH = "U:/AvatarFactory/"
PROJECT_AVATAR_FACTORY_PATH = get_config("avatar_factory_path")
IS_METAHUMAN_SPEC = get_config("is_metahuman_spec")
PUBLIC_LIB_PATH = os.path.join(PUBLIC_AVATAR_FACTORY_PATH, "lib")
PROJECT_LIB_PATH = os.path.join(PROJECT_AVATAR_FACTORY_PATH, "lib")

# Get configuration file from project path first, if not available, use public path
CONFIG_PATH = (
    os.path.join(PROJECT_AVATAR_FACTORY_PATH, "config")
    if PROJECT_AVATAR_FACTORY_PATH
    else os.path.join(PUBLIC_AVATAR_FACTORY_PATH, "config")
)

PUBLIC_RESOURCES_PATH = os.path.join(PUBLIC_AVATAR_FACTORY_PATH, "resources")
PROJECT_RESOURCES_PATH = os.path.join(PROJECT_AVATAR_FACTORY_PATH, "resources")
PUBLIC_MAKEUP_PATH = os.path.join(PUBLIC_RESOURCES_PATH, "textures/makeup")
PROJECT_MAKEUP_PATH = os.path.join(PROJECT_RESOURCES_PATH, "textures/makeup")

# Makeup library constants
SUPPORTED_MAKEUP_THUMBNAIL_EXTENSIONS = (".jpg", ".png", ".jpeg")
# Use an existing icon as default makeup thumbnail
DEFAULT_MAKEUP_THUMBNAIL = os.path.join(RESOURCE_ROOT, "resources/static/images/add_on.svg")

# Default local resource path (user configurable)
DEFAULT_LOCAL_LIB_PATH = ""

SOURCE_SUFFIX = ["source_T", "source_R", "source_L"]
PLUGINS_NAME = "embeddedRL4"
NODE_DNA_FILE = "dnaFilePath"
TRANMULTIPLIER = "translationMultiplier"
MAIN_CTRL_NAME = "CTRL_expressions"
ATTR_NAME_LIST = [
    "translateX",
    "translateY",
    "translateZ",
    "rotateX",
    "rotateY",
    "rotateZ",
    "scaleX",
    "scaleY",
    "scaleZ",
]
ATTR_LIST = [".tx", ".ty", ".tz", ".rx", ".ry", ".rz", ".sx", ".sy", ".sz"]
DNA_ATTR_LIST = ["jntTranslationOutputs", "jntRotationOutputs", "jntScaleOutputs"]
ORIENT_ATTR_LIST = [".jointOrientX", ".jointOrientY", ".jointOrientZ"]

REGION_TO_MODEL_MAP = {
    "eyes_l_blendshape": ["eyeLeft_lod0_mesh", "eyeEdge_lod0_mesh", "eyeshell_lod0_mesh"],
    "eyes_r_blendshape": ["eyeRight_lod0_mesh", "eyeEdge_lod0_mesh", "eyeshell_lod0_mesh"],
    "mouth_blendshape": ["teeth_lod0_mesh"],
}
OPEN_EDIT_MODE_LIST = ["打", "开", "编", "辑", "模", "式"]
OFF_EDIT_MODE_LIST = ["关", "闭", "编", "辑", "模", "式"]
HEAD_LAYER_NAME = "head_lod0_layer"
CONTROLLER_LAYER_NAME = "controller_layer"
RL_NODE_TYPE = "embeddedNodeRL4"
HEAD_GRP = "head_grp"
GEOMETRY_GRP = "geometry_grp"
HEADRIG_GRP = "headRig_grp"
PARAMETRIC_EYES_GRP = "parametric_eyes_grp"
ROOT_PINCH_JOINT = "root"
LIGHT_GRP = "_LIGHT"

EYE_SHAPE_DRIVER_L = "eye_shape_driver_L"
EYE_SHAPE_DRIVER_R = "eye_shape_driver_R"
EYE_DRIVER_L_ZERO = "eye_shape_driver_L_zero"
EYE_DRIVER_R_ZERO = "eye_shape_driver_R_zero"
CORNEA_DRIVER_L = "cornea_shape_driver_L"
CORNEA_DRIVER_R = "cornea_shape_driver_R"
CORNEA_DRIVER_L_ZERO = "cornea_shape_driver_L_zero"
CORNEA_DRIVER_R_ZERO = "cornea_shape_driver_R_zero"
EYE_DRIVEN_L = "eyeLeft_lod0_mesh"
EYE_DRIVEN_R = "eyeRight_lod0_mesh"

# Catchlight presets
CATCHLIGHT_GRP = "catchlight_grp"
CATCHLIGHT_PRESETS_GRP = "catchlight_presets_grp"
CATCHLIGHT_MATERIAL_NAME = "Mat_Catchlight"
PUBLIC_CATCHLIGHT_PATH = os.path.join(PUBLIC_RESOURCES_PATH, "models/catchlight/catchlight_presets.fbx")
# Fallback to local resource if public path doesn't exist
CATCHLIGHT_PATH = (
    PUBLIC_CATCHLIGHT_PATH
    if os.path.exists(PUBLIC_CATCHLIGHT_PATH)
    else os.path.join(RESOURCE_ROOT, "models/catchlight/catchlight_presets.fbx")
)

# Export_Util
HEAD_DIFFUSE_TEX_NAME = "head_color_map"

BASE_HEAD_MESH_NAME = "head_lod0_mesh"
BAK_HEAD_MESH_NAME = "head_lod0_mesh_bak"
WEIGHT_JSON_PATH = "vertex_weights.json"
LOD_GENERATE_JSON_PATH = "lod_generate_config.json"
REGION_CENTRAL_VERTEX_PATH = "region_central_vertex.json"
EYE_CONFIG_PATH = "parametric_eyes_config.json"
BASE_DNA_PATH = "base_character.dna"
PINCH_CTRL_NAME = "CTRL_pinch"
BLD_ZBRUSH_CMD = "bld -a cgm zbrush"
FACECUSTOMIZATION_PATH = os.path.join(RESOURCE_ROOT, "facecustomization")
FACEAREA_PATH = os.path.join(RESOURCE_ROOT, "face_area")
GOZRESOURES = os.path.join(PROJECT_AVATAR_FACTORY_PATH, "goz")
PIXOLOGIC_PATH = r"C:\Users\<USER>\Pixologic"
BLADE_THM_INSTALL_ENV = "BLADE_THM_INSTALL_PATH"
ALL_GOZ_MESH_NAME = "head_GOZ_mesh*"
GOZ_MESH_NAME = "head_GOZ_mesh"
ZBRUSH_FOLDERS = ["GoZApps", "GoZBrush"]
ZBRUSH_PORJECTS = ["GoZProjects", "Default"]
MAYA_APP_NAME = "Maya"
MEL_START = 'source "C:/Users/<USER>/Pixologic/GoZApps/Maya/GoZBrushFromMaya.mel"'
GOZ_CONFIG_FILE = "GoZ_Config.txt"
GOZ_MAYA_MEL = "GoZBrushToMaya.mel"
OUTPUT_DNA_NAME = "pinch"
DNA_NODE_NAME = f"embedded_{OUTPUT_DNA_NAME}_RL4"
GRP_ROOT = "_GRP"
FACE_CTRL_BASIC_GRP = "FACE_CTRL_BASIC_GRP"
FACE_CTRL_DETAILED_GRP = "FACE_CTRL_DETAILED_GRP"
FACE_CONTROL_JSON_FILES = ["DetailedCtrlPoints.json", "BasicCtrlPoints.json", "SocketsDefaultPositions.json"]
GOZ_BLEND_SHAPE_SUFFIX = "blendShape"

# Procrustes facial corresponding vertices
CORRESPONDENCE_INDICES = [20366, 23379, 2924, 12104, 2879, 2246, 5317, 2975, 3020, 9744]

MESH_GAP_KEY_NAME = "name"
MESH_GAP_KEY_T = "t"
MESH_GAP_KEY_L = "l"
MESH_GAP_KEY_R = "r"


class Direction(Enum):
    FORWARD = "forward"
    BACKWARD = "backward"
    IN = "in"
    OUT = "out"
    UP = "up"
    DOWN = "down"

    ROTATE_V = "rotate_V"
    ROTATE_A = "rotate_A"

    ENLARGE_H = "enlarge_H"
    REDUCE_H = "reduce_H"
    ENLARGE_V = "enlarge_V"
    REDUCE_V = "reduce_V"


class AttributeName(Enum):
    TRANSLATE_X = "translateX"
    TRANSLATE_Y = "translateY"
    TRANSLATE_Z = "translateZ"
    ROTATE_X = "rotateX"
    ROTATE_Y = "rotateY"
    ROTATE_Z = "rotateZ"
    SCALE_X = "scaleX"
    SCALE_Y = "scaleY"
    SCALE_Z = "scaleZ"


class DirectionKey(Enum):
    AXIS = "axis"
    VALUE = "value"
    SIGN = "sign"
    LEFT = "left"
    RIGHT = "right"
    TARGET = "target"
    BEGIN = "begin"


SUFFIX = ["_L", "_R"]
# Blendshape to region name mapping
PINCH_CTRL_ATTR = {
    "top_blendshape": "top",
    "forehead_blendshape": "forehead",
    "eyes_l_blendshape": "eye_l",
    "eyes_r_blendshape": "eye_r",
    "nose_blendshape": "nose",
    "mouth_blendshape": "mouth",
    "cheek_l_blendshape": "cheek_l",
    "cheek_r_blendshape": "cheek_r",
    "ear_l_blendshape": "ear_l",
    "ear_r_blendshape": "ear_r",
    "chin_blendshape": "chin",
    "lowerjaw_l_blendshape": "jaw_l",
    "lowerjaw_r_blendshape": "jaw_r",
    "neck_blendshape": "neck",
    "shoulder_blendshape": "shoulder",
}

# Face mirror area mapping table
MOCK_MIRROR_AREA_CONFIG = {
    "cheek": ["cheek_r", "cheek_l"],
    "eye": ["eye_r", "eye_l"],
    "ear": ["ear_r", "ear_l"],
    "jaw": ["jaw_r", "jaw_l"],
}

# Face mirror area data configuration
MOCK_MIRROR_AREA_CONFIG_LIST = [key for key, values in MOCK_MIRROR_AREA_CONFIG.items()]

# Non-mirrored facial area data configuration
MOCK_MIRROR_AREAS_NAME = [
    "all",
    "nose",
    "mouth",
    "chin",
    "cheek",
    "neck",
    "forehead",
    "eye",
    "top",
    "ear",
    "jaw",
]

MOCK_AREAS_NAME = ["all"] + [values for key, values in PINCH_CTRL_ATTR.items()]
ORGANIZATION_NAME = "Lightbox"

REQUIRED_DNA_COUNT = 4
DEBUG_MODE = False

DEFAULT_WINDOW_WIDTH = 1200
DEFAULT_WINDOW_HEIGHT = 800
DEFAULT_SPACING = 10
DEFAULT_MARGIN = 20

PRIMARY_COLOR = "#2d8cf0"
SUCCESS_COLOR = "#19be6b"
WARNING_COLOR = "#ff9900"
ERROR_COLOR = "#ed4014"

APP_PRIMARY_COLOR = "#2683d9"

# Log level
LOG_LEVEL = os.getenv("THM_LOG_LEVEL", "INFO")

# Project specific

NAMESPACE_PREFIX = "cgame"

# DNA Constants
DEFAULT_DNA_NUM = 4
DECIMAL_PLACES = 2

PACKAGES_RESOURCE_NAME = "CGAME_AVATAR_FACTORY_RESOURCE"
# DNA file extensions, must keep this const as a tuple
SUPPORTED_DNA_FILE_EXTENSIONS = (".dna", ".fbx", ".obj")
# Thumbnail file extensions, must keep this const as a tuple
SUPPORTED_THUMBNAIL_FILE_EXTENSIONS = (".jpg", ".png", ".jpeg")

# Temporary DNA file data configuration, affects UI generation and returned data format when adding DNA
MOCK_DNA_FILE_DATA_CONFIG = {
    "character_name": {
        "required": True,
        "hint_text": "请填写角色名",
        "hint_label": "角色名",
        "widget_type": "lineedit",
        "default": None,
    },
    "gender": {
        "required": False,
        "hint_text": "请填写角色性别",
        "hint_label": "角色性别",
        "widget_type": "combobox",
        "options": ["男", "女"],
        "default": "男",
    },
    "age": {
        "required": False,
        "hint_text": "请填写角色年龄",
        "hint_label": "角色年龄",
        "widget_type": "lineedit",
        "default": 18,
    },
    "race": {
        "required": False,
        "hint_text": "请填写角色种族",
        "hint_label": "角色种族",
        "widget_type": "combobox",
        "options": ["亚裔", "非裔", "拉美裔", "印第安人", "混血", "白人"],
        "default": None,
    },
    "is_official_dna": {
        "required": False,
        "hint_text": "是否为官方DNA",
        "hint_label": "是否为官方DNA？",
        "widget_type": "checkbox",
        "default": True,
    },
}

ALL_TYPE_NAME = "全部"
DNA_TYPE_ALL = {
    "公有库": {
        "small_icon": "art_center.png",
        "match": {
            "attr": "resource_path",
            "value": "PUBLIC_LIB_PATH",
        },
    },
    "项目库": {
        "small_icon": "og2.png",
        "match": {
            "attr": "resource_path",
            "value": "PROJECT_LIB_PATH",
        },
    },
    "个人库": {
        "small_icon": "local_library.png",
        "match": {
            "attr": "resource_path",
            "value": "LOCAL_LIB_PATH",
        },
    },
}

DEFAULT_DATA_LIST = {
    "age": 18,
    "character_name": "",
    "dna_file_path": ".DNA",
    "dna_id": 0,
    "gender": "女",
    "is_official_dna": True,
    "modified_date": None,
    "race": None,
    "thumbnail_file_path": "",
}

# Temporary DNA export configuration, affects export configuration UI generation and returned data format
MOCK_DNA_EXPORT_CONFIG = {
    "file_name": {
        "required": True,
        "hint_text": "请输入文件名",
        "hint_label": "文件名",
        "widget_type": "lineedit",
        "default": None,
    },
    "export_fbx": {
        "required": False,
        "hint_text": "同时导出FBX",
        "hint_label": "同时导出FBX？",
        "widget_type": "checkbox",
        "default": False,
    },
    "build_rig": {
        "required": False,
        "hint_text": "构建绑定",
        "hint_label": "构建绑定？",
        "widget_type": "checkbox",
        "default": False,
    },
}

# Facial controller name list
EXCEPT_CTRL = [
    "B_FACIAL_EYEV_L",
    "B_FACIAL_EYEV_R",
    "B_FACIAL_EYEH_R",
    "B_FACIAL_EYEH_L",
    "B_FACIAL_EYEROTATE_R",
    "B_FACIAL_EYEROTATE_L",
    "D_EYESHELL_CTRL_R",
    "D_EYESHELL_CTRL_L",
    "D_EYELASH_CTRL_R",
    "D_EYELASH_CTRL_L",
]

EXCEPT_CTRL_ATTR = [
    "Eye_RotateA_L",
    "Eye_RotateA_R",
    "Eye_RotateV_L",
    "Eye_RotateV_R",
    "Eye_ReduceVertical_L",
    "Eye_ReduceVertical_R",
    "Eye_EnlargeVertical_L",
    "Eye_EnlargeVertical_R",
    "Eye_ReduceHorizontal_L",
    "Eye_ReduceHorizontal_R",
    "Eye_EnlargeHorizontal_L",
    "Eye_EnlargeHorizontal_R",
    "Eye_Backward_L",
    "Eye_Backward_R",
    "Eye_Forward_L",
    "Eye_Forward_R",
    "Eye_In_L",
    "Eye_In_R",
    "Eye_Out_L",
    "Eye_Out_R",
    "Eye_Down_L",
    "Eye_Down_R",
    "Eye_Up_L",
    "Eye_Up_R",
]

ALL_CTRL = [
    "Ear_Backward_L",
    "Ear_Backward_R",
    "Ear_Forward_L",
    "Ear_Forward_R",
    "Ear_In_L",
    "Ear_In_R",
    "Ear_Out_L",
    "Ear_Out_R",
    "Ear_Down__L",
    "Ear_Down__R",
    "Ear_Up_L",
    "Ear_Up_R",
    "UnderChin_Down_M",
    "UnderChin_Up_M",
    "Jawline_Backward_R",
    "Jawline_Backward_L",
    "Jawline_Forward_R",
    "Jawline_Forward_L",
    "Jawline_In_L",
    "Jawline_In_R",
    "Jawline_Out_L",
    "Jawline_Out_R",
    "Chin_Backward_M",
    "Chin_Forward_M",
    "Chin_In_M",
    "Chin_Out_M",
    "Chin_Down_M",
    "Chin_Up_M",
    "Lips_Backward_M",
    "Lips_Forward_M",
    "Lips_In_M",
    "Lips_Out_M",
    "Lips_Down_M",
    "Lips_Up_M",
    "Cheek_Backward_L",
    "Cheek_Backward_R",
    "Cheek_Forward_L",
    "Cheek_Forward_R",
    "Cheek_In_L",
    "Cheek_In_R",
    "Cheek_Out_L",
    "Cheek_Out_R",
    "Cheek_Down_L",
    "Cheek_Down_R",
    "Cheek_Up_L",
    "Cheek_Up_R",
    "Nose_Backward_M",
    "Nose_Forward_M",
    "Nose_Reduce_M",
    "Nose_Enlarge_M",
    "Nose_Down_M",
    "Nose_Up_M",
    "Cheekbone_In_L",
    "Cheekbone_In_R",
    "Cheekbone_Out_L",
    "Cheekbone_Out_R",
    "Cheekbone_Down_L",
    "Cheekbone_Down_R",
    "Cheekbone_Up_L",
    "Cheekbone_Up_R",
    "Eye_RotateA_L",
    "Eye_RotateA_R",
    "Eye_RotateV_L",
    "Eye_RotateV_R",
    "Eye_ReduceVertical_L",
    "Eye_ReduceVertical_R",
    "Eye_EnlargeVertical_L",
    "Eye_EnlargeVertical_R",
    "Eye_ReduceHorizontal_L",
    "Eye_ReduceHorizontal_R",
    "Eye_EnlargeHorizontal_L",
    "Eye_EnlargeHorizontal_R",
    "Eye_Backward_L",
    "Eye_Backward_R",
    "Eye_Forward_L",
    "Eye_Forward_R",
    "Eye_In_L",
    "Eye_In_R",
    "Eye_Out_L",
    "Eye_Out_R",
    "Eye_Down_L",
    "Eye_Down_R",
    "Eye_Up_L",
    "Eye_Up_R",
    "Eyebrow_Backward_L",
    "Eyebrow_Backward_R",
    "Eyebrow_Forward_L",
    "Eyebrow_Forward_R",
    "Eyebrow_In_L",
    "Eyebrow_In_R",
    "Eyebrow_Out_L",
    "Eyebrow_Out_R",
    "Eyebrow_Down_L",
    "Eyebrow_Down_R",
    "Eyebrow_Up_L",
    "Eyebrow_Up_R",
    "Forehead_Backward_M",
    "Forehead_Forward_M",
    "Forehead_In_M",
    "Forehead_Out_M",
    "Forehead_Down_M",
    "Forehead_Up_M",
    "Face_In_M",
    "Face_Out_M",
    "Face_Down_M",
    "Face_Up_M",
    "EarLower_Backward_L",
    "EarLower_Backward_R",
    "EarLower_Forward_L",
    "EarLower_Forward_R",
    "EarLower_In_L",
    "EarLower_In_R",
    "EarLower_Out_L",
    "EarLower_Out_R",
    "EarLower_Down_L",
    "EarLower_Down_R",
    "EarLower_Up_L",
    "EarLower_Up_R",
    "EarMid_Backward_L",
    "EarMid_Backward_R",
    "EarMid_Forward_L",
    "EarMid_Forward_R",
    "EarMid_In_L",
    "EarMid_In_R",
    "EarMid_Out_L",
    "EarMid_Out_R",
    "EarMid_Down_L",
    "EarMid_Down_R",
    "EarMid_Up_L",
    "EarMid_Up_R",
    "EarUpper_In_L",
    "EarUpper_In_R",
    "EarUpper_Out_L",
    "EarUpper_Out_R",
    "EarUpper_Down_L",
    "EarUpper_Down_R",
    "EarUpper_Up_L",
    "EarUpper_Up_R",
    "LipsLowerMid_In_M",
    "LipsLowerMid_Out_M",
    "LipsLowerMid_Down_M",
    "LipsLowerMid_Up_M",
    "LipsLowerOut_In_L",
    "LipsLowerOut_In_R",
    "LipsLowerOut_Out_L",
    "LipsLowerOut_Out_R",
    "LipsLowerOut_Down_L",
    "LipsLowerOut_Down_R",
    "LipsLowerOut_Up_L",
    "LipsLowerOut_Up_R",
    "LipsUpperCorner_In_L",
    "LipsUpperCorner_In_R",
    "LipsUpperCorner_Out_L",
    "LipsUpperCorner_Out_R",
    "LipsUpperCorner_Down_L",
    "LipsUpperCorner_Down_R",
    "LipsUpperCorner_Up_L",
    "LipsUpperCorner_Up_R",
    "LipsUpperOut_In_L",
    "LipsUpperOut_In_R",
    "LipsUpperOut_Out_L",
    "LipsUpperOut_Out_R",
    "LipsUpperOut_Down_L",
    "LipsUpperOut_Down_R",
    "LipsUpperOut_Up_L",
    "LipsUpperOut_Up_R",
    "LipsUpperMid_In_M",
    "LipsUpperMid_Out_M",
    "LipsUpperMid_Down_M",
    "LipsUpperMid_Up_M",
    "Philtrum_In_M",
    "Philtrum_Out_M",
    "Philtrum_Down_M",
    "Philtrum_Up_M",
    "Nostril_Backward_L",
    "Nostril_Backward_R",
    "Nostril_Forward_L",
    "Nostril_Forward_R",
    "Nostril_In_L",
    "Nostril_In_R",
    "Nostril_Out_L",
    "Nostril_Out_R",
    "Nostril_Down_L",
    "Nostril_Down_R",
    "Nostril_Up_L",
    "Nostril_Up_R",
    "NoseTip_Backward_M",
    "NoseTip_Forward_M",
    "NoseTip_In_M",
    "NoseTip_Out_M",
    "NoseTip_Down_M",
    "NoseTip_Up_M",
    "NasalBridgeMid_Backward_M",
    "NasalBridgeMid_Forward_M",
    "NasalBridgeMid_In_M",
    "NasalBridgeMid_Out_M",
    "NasalBridgeMid_Down_M",
    "NasalBridgeMid_Up_M",
    "NasalBridgeUpper_Backward_M",
    "NasalBridgeUpper_Forward_M",
    "NasalBridgeUpper_In_M",
    "NasalBridgeUpper_Out_M",
    "NasalBridgeUpper_Down_M",
    "NasalBridgeUpper_Up_M",
    "Eyelash_RotateDown_L",
    "Eyelash_RotateDown_R",
    "Eyelash_RotateUp_L",
    "Eyelash_RotateUp_R",
    "EyelidLowerOut_In_L",
    "EyelidLowerOut_In_R",
    "EyelidLowerOut_Out_L",
    "EyelidLowerOut_Out_R",
    "EyelidLowerOut_Down_L",
    "EyelidLowerOut_Down_R",
    "EyelidLowerOut_Up_L",
    "EyelidLowerOut_Up_R",
    "EyelidLowerInn_In_L",
    "EyelidLowerInn_In_R",
    "EyelidLowerInn_Out_L",
    "EyelidLowerInn_Out_R",
    "EyelidLowerInn_Down_L",
    "EyelidLowerInn_Down_R",
    "EyelidLowerInn_Up_L",
    "EyelidLowerInn_Up_R",
    "EyelidLowerMid_In_L",
    "EyelidLowerMid_In_R",
    "EyelidLowerMid_Out_L",
    "EyelidLowerMid_Out_R",
    "EyelidLowerMid_Down_L",
    "EyelidLowerMid_Down_R",
    "EyelidLowerMid_Up_L",
    "EyelidLowerMid_Up_R",
    "EyelidCornerOut_In_L",
    "EyelidCornerOut_In_R",
    "EyelidCornerOut_Out_L",
    "EyelidCornerOut_Out_R",
    "EyelidCornerOut_Down_L",
    "EyelidCornerOut_Down_R",
    "EyelidCornerOut_Up_L",
    "EyelidCornerOut_Up_R",
    "EyelidUpperOut_In_L",
    "EyelidUpperOut_In_R",
    "EyelidUpperOut_Out_L",
    "EyelidUpperOut_Out_R",
    "EyelidUpperOut_Down_L",
    "EyelidUpperOut_Down_R",
    "EyelidUpperOut_Up_L",
    "EyelidUpperOut_Up_R",
    "EyelidCornerInn_In_L",
    "EyelidCornerInn_In_R",
    "EyelidCornerInn_Out_L",
    "EyelidCornerInn_Out_R",
    "EyelidCornerInn_Down_L",
    "EyelidCornerInn_Down_R",
    "EyelidCornerInn_Up_L",
    "EyelidCornerInn_Up_R",
    "EyelidUpperInn_In_L",
    "EyelidUpperInn_In_R",
    "EyelidUpperInn_Out_L",
    "EyelidUpperInn_Out_R",
    "EyelidUpperInn_Down_L",
    "EyelidUpperInn_Down_R",
    "EyelidUpperInn_Up_L",
    "EyelidUpperInn_Up_R",
    "EyelidUpperMid_In_L",
    "EyelidUpperMid_In_R",
    "EyelidUpperMid_Out_L",
    "EyelidUpperMid_Out_R",
    "EyelidUpperMid_Down_L",
    "EyelidUpperMid_Down_R",
    "EyelidUpperMid_Up_L",
    "EyelidUpperMid_Up_R",
    "EyesackUpper_In_L",
    "EyesackUpper_In_R",
    "EyesackUpper_Out_L",
    "EyesackUpper_Out_R",
    "EyesackUpper_Down_L",
    "EyesackUpper_Down_R",
    "EyesackUpper_Up_L",
    "EyesackUpper_Up_R",
    "EyebrowOut_Backward_L",
    "EyebrowOut_Backward_R",
    "EyebrowOut_Forward_L",
    "EyebrowOut_Forward_R",
    "EyebrowOut_In_L",
    "EyebrowOut_In_R",
    "EyebrowOut_Out_L",
    "EyebrowOut_Out_R",
    "EyebrowOut_Down_L",
    "EyebrowOut_Down_R",
    "EyebrowOut_Up_L",
    "EyebrowOut_Up_R",
    "EyebrowMid_Backward_L",
    "EyebrowMid_Backward_R",
    "EyebrowMid_Forward_L",
    "EyebrowMid_Forward_R",
    "EyebrowMid_In_L",
    "EyebrowMid_In_R",
    "EyebrowMid_Out_L",
    "EyebrowMid_Out_R",
    "EyebrowMid_Down_L",
    "EyebrowMid_Down_R",
    "EyebrowMid_Up_L",
    "EyebrowMid_Up_R",
    "EyebrowInn_Backward_L",
    "EyebrowInn_Backward_R",
    "EyebrowInn_Forward_L",
    "EyebrowInn_Forward_R",
    "EyebrowInn_In_L",
    "EyebrowInn_In_R",
    "EyebrowInn_Out_L",
    "EyebrowInn_Out_R",
    "EyebrowInn_Down_L",
    "EyebrowInn_Down_R",
    "EyebrowInn_Up_L",
    "EyebrowInn_Up_R",
]
