# from pstats import FunctionProfile
# Import built-in modules
import concurrent.futures
import os
from typing import Dict
from typing import List

# Import third-party modules
import maya.api.OpenMaya as om2
import maya.cmds as cmds
import numpy as np

# Import local modules
from cgame_avatar_factory import utils
import cgame_avatar_factory.api.dna_utils as dna
import cgame_avatar_factory.constants as const


class BuildFinallyMesh:
    """Build final mesh with blendshapes from multiple source meshes

    Creates blendshapes and controls for morphing between multiple source meshes.
    Handles vertex weights, mesh gaps, and control attributes.

    Args:
        all_mesh_data: List of dictionaries containing mesh vertex data
    """

    def __init__(self, all_mesh_data: List[Dict[str, List[np.array]]]) -> None:
        """Initialize mesh builder with source data"""
        self.all_mesh_data = all_mesh_data
        self.source_dna_path = os.path.join(const.CONFIG_PATH, const.BASE_DNA_PATH)
        self.mesh_name_list = dna.get_mesh_name(self.source_dna_path)
        self.mesh_arrays = self._create_mesh_arrays()

        self.distance_mesh_data = self._get_mesh_gap()

        vertex_weights_path = os.path.join(const.CONFIG_PATH, const.WEIGHT_JSON_PATH)
        self.vertex_weights = utils.read_json(vertex_weights_path)
        self._analyze_data()

    def _create_mesh_arrays(self):
        """Create mesh arrays dictionary from source data

        Returns:
            dict: Dictionary mapping mesh names to lists of vertex arrays
        """
        mesh_arrays = {}
        for mesh_name in self.mesh_name_list:
            mesh_data_arrays = []
            mesh_exists_in_all = True

            for i in range(const.REQUIRED_DNA_COUNT):
                if mesh_name not in self.all_mesh_data[i]:
                    mesh_exists_in_all = False
                    break
                mesh_data_arrays.append(np.array(self.all_mesh_data[i][mesh_name]))

            if mesh_exists_in_all:
                mesh_arrays[mesh_name] = mesh_data_arrays
        return mesh_arrays

    def _get_mesh_gap(self):
        distance = []
        mesh0t_distance = {}
        mesh0l_distance = {}
        mesh0r_distance = {}

        def process_mesh(mesh_name):
            mesh0, mesh1, mesh2, mesh3 = self.mesh_arrays[mesh_name]
            return {
                const.MESH_GAP_KEY_NAME: mesh_name,
                const.MESH_GAP_KEY_T: np.subtract(mesh0, mesh1),
                const.MESH_GAP_KEY_L: np.subtract(mesh0, mesh2),
                const.MESH_GAP_KEY_R: np.subtract(mesh0, mesh3),
            }

        valid_mesh_names = [mesh_name for mesh_name in self.mesh_name_list if mesh_name in self.mesh_arrays]

        with concurrent.futures.ThreadPoolExecutor() as executor:
            results = list(executor.map(process_mesh, valid_mesh_names))

        for result in results:
            mesh_name = result[const.MESH_GAP_KEY_NAME]
            mesh0t_distance[mesh_name] = result[const.MESH_GAP_KEY_T]
            mesh0l_distance[mesh_name] = result[const.MESH_GAP_KEY_L]
            mesh0r_distance[mesh_name] = result[const.MESH_GAP_KEY_R]

        distance.extend([mesh0t_distance, mesh0l_distance, mesh0r_distance])
        return distance

    def _analyze_data(self):
        """Process vertex weights and create blendshapes

        Creates blendshapes for each mesh using vertex weights and
        connects them to the pinch control.
        """
        pinch_ctrl = self.create_pinch_ctrl()
        for mesh_name, mesh_value in self.vertex_weights.items():
            if cmds.objExists(mesh_name):
                for blendshape_name, weights in mesh_value.items():
                    blend_node = self._get_or_create_blend_node(mesh_name)
                    self._create_blendshape(mesh_name, blendshape_name, weights, blend_node, pinch_ctrl)

    def _create_blendshape(
        self,
        mesh_name: str,
        blendshape_name: str,
        weights: dict,
        blend_node: str,
        pinch_ctrl: str,
    ):
        """Create blendshape for a mesh with given weights

        Args:
            mesh_name: Name of base mesh
            blendshape_name: Name for blendshape
            weights: Dictionary of vertex weights
            blend_node: Name of blend node
            pinch_ctrl: Name of pinch control
        """
        for mesh_data_index, suffix in enumerate(const.SOURCE_SUFFIX):
            if mesh_data_index >= len(self.distance_mesh_data):
                break
            # Create target model based on mesh name + blendshape name + "source_T", "source_R", "source_L" for blendshape
            target_name = f"{mesh_name}_{blendshape_name}_{suffix}"
            target_mesh = cmds.duplicate(mesh_name, name=target_name)[0]

            self._calculate_target_point_position(
                mesh_name,
                weights,
                self.distance_mesh_data[mesh_data_index][mesh_name],
                target_mesh,
            )

            weight_index = self._get_weight_index(blend_node)
            cmds.blendShape(
                blend_node,
                edit=True,
                target=(mesh_name, weight_index, target_mesh, 1.0),
            )
            cmds.setAttr(f"{blend_node}.w[{weight_index}]", 0)
            cmds.setAttr(f"{target_mesh}.visibility", 0)

            src_attr = f"{pinch_ctrl}.{const.PINCH_CTRL_ATTR[blendshape_name]}_{suffix}"
            dst_attr = f"{blend_node}.w[{weight_index}]"
            cmds.connectAttr(src_attr, dst_attr)
            cmds.delete(target_mesh)

    def _get_or_create_blend_node(self, mesh_name: str):
        """Get existing blend node or create a new one

        Args:
            mesh_name: Name of mesh to get/create blend node for

        Returns:
            str: Name of blend node
        """
        history = cmds.listHistory(mesh_name, pdo=True) or []
        for node in history:
            if cmds.nodeType(node) == "blendShape":
                return node
        return cmds.blendShape(mesh_name, name=f"{mesh_name}_blendShape", before=True)[0]

    def _get_weight_index(self, blend_node: str):
        """Get next available weight index for blend node

        Args:
            blend_node: Name of blend node

        Returns:
            int: Next available weight index
        """
        weights = cmds.blendShape(blend_node, query=True, weight=True)
        return 0 if not weights else len(weights)

    def _calculate_target_point_position(self, mesh_name, weights, distance_mesh_data, target_mesh):
        vertex_indices = [int(idx) for idx in weights.keys()]
        weight_values = np.array([weights[str(idx)] for idx in vertex_indices])
        base_positions = np.array([self.all_mesh_data[0][mesh_name][idx] for idx in vertex_indices])
        distances = np.array([distance_mesh_data[idx] for idx in vertex_indices])
        new_positions = base_positions - (distances * weight_values[:, np.newaxis])

        selection = om2.MSelectionList()
        selection.add(target_mesh)
        dag_path = selection.getDagPath(0)
        mesh_fn = om2.MFnMesh(dag_path)

        for i, idx in enumerate(vertex_indices):
            pos = new_positions[i]
            point = om2.MPoint(pos[0], pos[1], pos[2])
            mesh_fn.setPoint(idx, point)

    def create_pinch_ctrl(self):
        """Create or get the pinch control

        Creates a control node with attributes for controlling
        blendshape weights if it doesn't exist.

        Returns:
            str: Name of pinch control node
        """
        if cmds.objExists(const.PINCH_CTRL_NAME):
            return const.PINCH_CTRL_NAME

        pinch_ctrl = cmds.createNode("transform", name=const.PINCH_CTRL_NAME)

        for attr in const.ATTR_LIST:
            cmds.setAttr(f"{pinch_ctrl}{attr}", lock=True, keyable=False, channelBox=False)

        cmds.setAttr(f"{pinch_ctrl}.visibility", 0, lock=True, keyable=False, channelBox=False)

        for area_name, attr_name in const.PINCH_CTRL_ATTR.items():
            for suffix in const.SOURCE_SUFFIX:
                full_attr = f"{attr_name}_{suffix}"
                if not cmds.attributeQuery(full_attr, node=pinch_ctrl, exists=True):
                    cmds.addAttr(pinch_ctrl, longName=full_attr, attributeType="float", minValue=0, maxValue=1)
                    cmds.setAttr(f"{pinch_ctrl}.{full_attr}", keyable=True)

        return pinch_ctrl
