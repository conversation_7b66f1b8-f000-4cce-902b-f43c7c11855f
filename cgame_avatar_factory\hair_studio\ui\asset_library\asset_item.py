"""Asset Item Module.

This module provides the AssetItem widget for displaying individual hair assets
in the asset library grid.
"""

# Import standard library
import os

# Import Qt modules
from qtpy import QtWidgets, QtCore, QtGui

# Import dayu widgets
from dayu_widgets import <PERSON>Label, MToolButton, MFlowLayout, dayu_theme
from dayu_widgets.qt import MIcon

# Import local modules
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager


class AssetItem(QtWidgets.QWidget):
    """Asset Item Widget.
    
    This widget represents a single hair asset in the asset library.
    It displays a thumbnail and the asset name, and emits a signal when clicked.
    """
    
    # Signal emitted when the item is clicked
    clicked = QtCore.Signal(dict)
    
    def __init__(self, asset_data, parent=None):
        """Initialize the AssetItem.
        
        Args:
            asset_data (dict): Dictionary containing asset data
            parent (QWidget, optional): The parent widget. Defaults to None.
        """
        super(AssetItem, self).__init__(parent)
        self.asset_data = asset_data
        self.manager = HairManager()
        
        # Set up UI
        self.setup_ui()
        self.setSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        
        # Set up drag and drop
        self.setAcceptDrops(True)
    
    def setup_ui(self):
        """Set up the user interface components."""
        # Main layout
        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # Thumbnail (placeholder for now)
        self.thumbnail_label = QtWidgets.QLabel()
        self.thumbnail_label.setFixedSize(100, 100)
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                background-color: #2D2D2D;
                border: 1px solid #3E3E3E;
                border-radius: 3px;
            }
        """)
        
        # Set placeholder text
        self.thumbnail_label.setText("Thumbnail")
        self.thumbnail_label.setAlignment(QtCore.Qt.AlignCenter)
        
        # TODO: Load actual thumbnail from asset data
        
        # Name label
        self.name_label = MLabel(self.asset_data.get('name', 'Unnamed Asset'))
        self.name_label.setAlignment(QtCore.Qt.AlignCenter)
        self.name_label.set_elide_mode(QtCore.Qt.ElideRight)
        self.name_label.setToolTip(self.name_label.text())
        
        # Add widgets to layout
        layout.addWidget(self.thumbnail_label, 0, QtCore.Qt.AlignHCenter)
        layout.addWidget(self.name_label, 0, QtCore.Qt.AlignHCenter)
        
        # Set style
        self.setStyleSheet("""
            QWidget {
                background: transparent;
                border: 1px solid transparent;
                border-radius: 4px;
            }
            QWidget:hover {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid #3E3E3E;
            }
        """)
    
    def mousePressEvent(self, event):
        """Handle mouse press events."""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.asset_data)
        
        super(AssetItem, self).mousePressEvent(event)
    
    def mouseDoubleClickEvent(self, event):
        """Handle mouse double click events."""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.asset_data)
        
        super(AssetItem, self).mouseDoubleClickEvent(event)
    
    def get_asset_data(self):
        """Get the asset data for this item.
        
        Returns:
            dict: The asset data
        """
        return self.asset_data
    
    def set_thumbnail(self, pixmap):
        """Set the thumbnail image for this asset.
        
        Args:
            pixmap (QPixmap): The thumbnail image
        """
        if pixmap and not pixmap.isNull():
            scaled_pixmap = pixmap.scaled(
                self.thumbnail_label.size(),
                QtCore.Qt.KeepAspectRatio,
                QtCore.Qt.SmoothTransformation
            )
            self.thumbnail_label.setPixmap(scaled_pixmap)
            self.thumbnail_label.setText("")  # Clear placeholder text