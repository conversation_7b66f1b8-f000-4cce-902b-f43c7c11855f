"""Create and setup riglogic node."""
# Import future modules
from __future__ import annotations

# Import built-in modules
import logging

# Import third-party modules
# Import maya modules
from maya import cmds

# Import local modules
import cgame_avatar_factory.api.dna_utils as dna
import cgame_avatar_factory.constants as const


class SetupRiglogic:
    """Set up riglogic node and controls for DNA-based character

    Creates and configures a riglogic node that drives character joints
    based on DNA file data. Sets up control attributes and connections.

    Args:
        dna_path: Path to DNA file
    """

    def __init__(self, dna_path: str):
        """Initialize riglogic setup

        Args:
            dna_path: Path to DNA file
        """
        self.dna_path = dna_path
        self.reader = dna.TemplateDna.get_reader()
        self.setup()

    def setup(self):
        """Set up riglogic node and controls

        Creates riglogic node, main control, and sets up attributes.

        Raises:
            RuntimeError: If riglogic node creation fails
        """
        self.node = self.create_node()
        if not self.node:
            raise RuntimeError("Failed to create riglogic node")
        self.create_main_ctrl()
        self.set_node_attributes()

    def set_node_attributes(self):
        """Set up riglogic node attributes

        Sets DNA path, translation multiplier, and connects joint attributes.
        """
        self.set_dna_path()
        self.set_translation_multiplier()
        self.connect_joints_attr()

    def set_dna_path(self):
        """Set DNA file path attribute on riglogic node"""
        cmds.setAttr(f"{self.node}.{const.NODE_DNA_FILE}", self.dna_path, type="string")

    def set_translation_multiplier(self):
        """Set translation multiplier attribute on riglogic node"""
        cmds.setAttr(f"{self.node}.{const.TRANMULTIPLIER}", 1.0)

    def create_node(self):
        """Create riglogic node

        Returns:
            str: Name of created node, or None if creation fails
        """
        if not cmds.pluginInfo(const.PLUGINS_NAME, query=True, loaded=True):
            try:
                cmds.loadPlugin(const.PLUGINS_NAME)
            except Exception as e:
                logging.error(f"Failed to load embeddedRL4 plugin: {str(e)}")
                return None

        node = cmds.createNode(const.RL_NODE_TYPE, name=const.DNA_NODE_NAME)
        return node

    def create_main_ctrl(self):
        """Create main control and set up raw control attributes

        Creates main control if it doesn't exist and adds attributes
        for each raw control from the DNA file.
        """
        if cmds.objExists(const.MAIN_CTRL_NAME):
            cmds.delete(const.MAIN_CTRL_NAME)

        cmds.group(name=const.MAIN_CTRL_NAME, empty=True)
        for attr in const.ATTR_LIST:
            cmds.setAttr(f"{const.MAIN_CTRL_NAME}{attr}", lock=True, keyable=False, channelBox=False)
        cmds.setAttr(f"{const.MAIN_CTRL_NAME}.v", 0, lock=True, keyable=False, channelBox=False)

        raw_control_count = self.reader.getRawControlCount()
        for raw_index in range(raw_control_count):
            raw_control_name = self.reader.getRawControlName(raw_index)
            ctrl_name, attr_name = raw_control_name.split(".")

            cmds.addAttr(
                ctrl_name,
                longName=attr_name,
                attributeType="float",
                minValue=0.0,
                maxValue=1.0,
                defaultValue=0.0,
                keyable=True,
            )

            src_attr = f"{ctrl_name}.{attr_name}"
            dst_attr = f"{self.node}.inputs[{raw_index}]"
            cmds.connectAttr(src_attr, dst_attr)

    def clear_connected_array_attribute(self, node, array_attr_name):
        """Clear all elements from an array attribute

        Args:
            node: Node with array attribute
            array_attr_name: Name of array attribute to clear
        """
        attr_elements = cmds.getAttr(f"{node}.{array_attr_name}", multiIndices=True)
        if attr_elements:
            for i in attr_elements:
                attr_element = f"{node}.{array_attr_name}[{i}]"
                cmds.removeMultiInstance(attr_element, b=True)

    def connect_joints_attr(self):
        """Connect joint attributes to riglogic node

        Sets up connections between riglogic node outputs and joint
        translate, rotate and scale attributes based on DNA data.
        """
        self.clear_connected_array_attribute(self.node, const.DNA_ATTR_LIST[0])
        self.clear_connected_array_attribute(self.node, const.DNA_ATTR_LIST[1])
        self.clear_connected_array_attribute(self.node, const.DNA_ATTR_LIST[2])

        translation_output_index = 0
        rotation_output_index = 0
        scale_output_index = 0
        attrs = ["x", "y", "z"]

        for i in range(self.reader.getJointGroupCount()):
            out_ids_list = self.reader.getJointGroupOutputIndices(i)
            for out_id in out_ids_list:
                jnt_id = int(out_id / 9)
                joint_name = self.reader.getJointName(jnt_id)
                remainder = out_id % 9

                if remainder < 3:
                    jnt_plug = f"{joint_name}.t{attrs[remainder]}"
                    cmds.connectAttr(f"{self.node}.{const.DNA_ATTR_LIST[0]}[{translation_output_index}]", jnt_plug)
                    translation_output_index += 1
                elif remainder < 6:
                    jnt_plug = f"{joint_name}.r{attrs[remainder - 3]}"
                    cmds.connectAttr(f"{self.node}.{const.DNA_ATTR_LIST[1]}[{rotation_output_index}]", jnt_plug)
                    rotation_output_index += 1
                else:
                    jnt_plug = f"{joint_name}.s{attrs[remainder - 6]}"
                    cmds.connectAttr(f"{self.node}.{const.DNA_ATTR_LIST[2]}[{scale_output_index}]", jnt_plug)
                    scale_output_index += 1
