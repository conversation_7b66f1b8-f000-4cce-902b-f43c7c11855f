"""Component List Module.

This module provides the component list widget for the Hair Studio tool.
It displays the list of hair components that have been added to the scene.
"""

# Import standard library
import os

# Import Qt modules
from qtpy import QtWidgets, QtCore

# Import dayu widgets
from dayu_widgets import MLabel, MToolButton, MFlowLayout, MListView
from dayu_widgets.qt import MIcon

# Import local modules
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager


class ComponentList(QtWidgets.QWidget):
    """Component List Widget.

    This widget displays a list of hair components that have been added to the scene.
    Users can select components to edit their properties in the editor area.
    """

    # Signal emitted when a component is selected
    component_selected = QtCore.Signal(dict)

    def __init__(self, hair_type, hair_manager=None, parent=None):
        """Initialize the ComponentList.

        Args:
            hair_type (str): Type of hair ('card', 'xgen', 'curve')
            hair_manager (HairManager, optional): The hair manager instance. If None, a new one will be created.
            parent (QWidget, optional): The parent widget. Defaults to None.
        """
        super(ComponentList, self).__init__(parent)
        self.hair_type = hair_type
        self.object_name = "{}ComponentList".format(hair_type.capitalize())
        self.setObjectName(self.object_name)

        # Initialize manager
        self.manager = hair_manager if hair_manager is not None else HairManager()

        # Initialize UI
        self.setup_ui()

    def setup_ui(self):
        """Set up the user interface components."""
        # Main layout
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(2)

        # Title
        title = MLabel("毛发组件列表")
        title.setProperty("h2", True)
        main_layout.addWidget(title)

        # Component list
        self.component_list = MListView()
        self.component_list.setObjectName("{}_component_list".format(self.hair_type))
        self.component_list.clicked.connect(self._on_component_clicked)
        main_layout.addWidget(self.component_list)

        # Buttons
        button_layout = MFlowLayout()

        self.add_button = MToolButton()
        self.add_button.setIcon(MIcon("add_line.svg"))
        self.add_button.clicked.connect(self._on_add_component)
        button_layout.addWidget(self.add_button)

        self.remove_button = MToolButton()
        self.remove_button.setIcon(MIcon("trash_line.svg"))
        self.remove_button.clicked.connect(self._on_remove_component)
        button_layout.addWidget(self.remove_button)

        main_layout.addLayout(button_layout)

    def add_component(self, asset_data):
        """Add a new component to the list.

        Args:
            asset_data (dict): Dictionary containing asset data
        """
        if not asset_data:
            return

        # Create component using manager
        component = self.manager.create_component(
            asset_type=self.hair_type,
            asset_id=asset_data.get("id"),
            name=asset_data.get("name"),
        )

        # Add to list
        item = self._create_component_item(component)
        self.component_list.addItem(item)

        # Select the new item
        self.component_list.setCurrentItem(item)
        self._on_component_clicked(item)

    def _create_component_item(self, component):
        """Create a list item for the given component.

        Args:
            component (dict): The component data

        Returns:
            QtWidgets.QListWidgetItem: The created list item
        """
        item = QtWidgets.QListWidgetItem(component.get("name", "Unnamed Component"))
        item.setData(QtCore.Qt.UserRole, component)
        return item

    def _on_component_clicked(self, index):
        """Handle component item click event.

        Args:
            index (QtCore.QModelIndex): The index of the clicked item
        """
        if not index.isValid():
            return

        # Get component data from model
        model = self.component_list.model()
        component = model.data(index, QtCore.Qt.UserRole)
        if component:
            self.component_selected.emit(component)

    def _on_add_component(self):
        """Handle add component button click."""
        # This will be implemented to show the asset browser
        pass

    def _on_remove_component(self):
        """Handle remove component button click."""
        current_item = self.component_list.currentItem()
        if current_item:
            row = self.component_list.row(current_item)
            self.component_list.takeItem(row)

            # Emit signal with None to clear the editor
            self.component_selected.emit(None)

    def clear_components(self):
        """Clear all components from the list."""
        self.component_list.clear()
        self.component_selected.emit(None)

    def get_selected_component(self):
        """Get the currently selected component.

        Returns:
            dict: The selected component data, or None if no selection
        """
        current_item = self.component_list.currentItem()
        if current_item:
            return current_item.data(256)  # 256 = Qt.UserRole
        return None
