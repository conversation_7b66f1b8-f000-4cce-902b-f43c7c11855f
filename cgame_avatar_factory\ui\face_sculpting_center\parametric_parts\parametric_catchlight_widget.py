# -*- coding: utf-8 -*-
"""
Eye Light Widget for parametric eye light management.
"""

# Import built-in modules
import logging
import os

# Import third-party modules
import dayu_widgets
from dayu_widgets import <PERSON><PERSON>abe<PERSON>
from dayu_widgets import MMessage
from dayu_widgets import MSlider
from dayu_widgets import MToolButton
import maya.cmds as cmds
from qtpy import Qt<PERSON>ore
from qtpy import QtGui
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.api.parametric_parts.parametric_catchlight import ParametricCatchlight
from cgame_avatar_factory.ui.components.layout import FramelessHLayout
from cgame_avatar_factory.ui.components.layout import FramelessVLayout

# Constants for item sizing
THUMBNAIL_SIZE = 80
TEXT_HEIGHT = 24
ITEM_SPACING = 8
ICON_SIZE = 80
GRID_WIDTH = 100
GRID_HEIGHT = 120

LIST_ITEM_HEIGHT = 66
LIST_ITEM_CONTENT_HEIGHT = 56


class CatchlightRotateWidget(QtWidgets.QWidget):
    """Widget for displaying a coordinate system with a draggable control point for catchlight rotation."""

    # Signal emitted when the control point is moved
    pointMoved = QtCore.Signal(QtCore.QPointF)

    def __init__(self, parent=None):
        super(CatchlightRotateWidget, self).__init__(parent)
        self.control_point = QtCore.QPointF(0, 0)  # Initial position at origin
        self.dragging = False
        self.grid_size = 15  # Grid size in pixels
        self.point_radius = 4  # Control point radius
        self.point_color = QtGui.QColor("#1890FF")  # Default color for control point

        # Set up anti-aliasing
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground)
        self.setAutoFillBackground(True)

    def sizeHint(self):
        """Return the preferred size hint."""
        return QtCore.QSize(200, 200)

    def minimumSizeHint(self):
        """Return the minimum size hint."""
        return QtCore.QSize(80, 80)

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # Draw background
        painter.fillRect(self.rect(), QtGui.QColor(35, 35, 35))

        # Draw grid
        self.draw_grid(painter)

        # Draw coordinate axes
        self.draw_axes(painter)

        # Draw control point
        self.draw_control_point(painter)

    def draw_grid(self, painter):
        painter.save()
        pen = QtGui.QPen(QtGui.QColor(60, 60, 60), 1, QtCore.Qt.DotLine)
        painter.setPen(pen)

        width = self.width()
        height = self.height()
        center = self.get_center()

        # Calculate adaptive grid size based on widget size
        adaptive_grid_size = max(15, min(width, height) // 8)  # Adaptive grid size

        # Vertical grid lines
        x = center.x() % adaptive_grid_size
        while x < width:
            painter.drawLine(x, 0, x, height)
            x += adaptive_grid_size

        # Horizontal grid lines
        y = center.y() % adaptive_grid_size
        while y < height:
            painter.drawLine(0, y, width, y)
            y += adaptive_grid_size
        painter.restore()

    def draw_axes(self, painter):
        painter.save()
        center = self.get_center()

        # Draw X axis
        painter.setPen(QtGui.QPen(QtGui.QColor("#5E5E5E"), 2))
        painter.drawLine(0, center.y(), self.width(), center.y())

        # Draw Y axis
        painter.setPen(QtGui.QPen(QtGui.QColor("#5E5E5E"), 2))
        painter.drawLine(center.x(), 0, center.x(), self.height())

        painter.restore()

    def draw_control_point(self, painter):
        painter.save()
        painter.setPen(QtGui.QPen(self.point_color, 2))
        painter.setBrush(self.point_color)

        center = self.get_center()

        # Convert normalized coordinates (-1 to 1) back to screen coordinates
        max_radius = min(center.x(), center.y()) - max(4, min(self.width(), self.height()) // 20)
        screen_x = center.x() + self.control_point.x() * max_radius
        screen_y = center.y() - self.control_point.y() * max_radius  # Y axis is inverted

        point_pos = QtCore.QPointF(screen_x, screen_y)

        # Calculate adaptive point radius based on widget size
        adaptive_radius = max(4, min(self.width(), self.height()) // 20)
        painter.drawEllipse(point_pos, adaptive_radius, adaptive_radius)
        painter.restore()

    def get_center(self):
        return QtCore.QPointF(self.width() / 2, self.height() / 2)

    def mousePressEvent(self, event):
        center = self.get_center()
        widget_pos = event.pos()

        # Convert to normalized coordinate system (-1 to 1, center is origin, Y axis points up)
        max_radius = min(center.x(), center.y()) - max(4, min(self.width(), self.height()) // 20)
        normalized_x = (widget_pos.x() - center.x()) / max_radius
        normalized_y = (center.y() - widget_pos.y()) / max_radius

        # Clamp to -1 to 1 range
        normalized_x = max(-1.0, min(normalized_x, 1.0))
        normalized_y = max(-1.0, min(normalized_y, 1.0))

        # Move control point to clicked position
        self.control_point = QtCore.QPointF(normalized_x, normalized_y)
        self.pointMoved.emit(self.control_point)
        self.update()

        # Start dragging
        self.dragging = True

    def mouseMoveEvent(self, event):
        if self.dragging:
            center = self.get_center()
            widget_pos = event.pos()

            # Convert to normalized coordinate system (-1 to 1, center is origin, Y axis points up)
            max_radius = min(center.x(), center.y()) - max(4, min(self.width(), self.height()) // 20)
            normalized_x = (widget_pos.x() - center.x()) / max_radius
            normalized_y = (center.y() - widget_pos.y()) / max_radius

            # Clamp to -1 to 1 range
            normalized_x = max(-1.0, min(normalized_x, 1.0))
            normalized_y = max(-1.0, min(normalized_y, 1.0))

            self.control_point = QtCore.QPointF(normalized_x, normalized_y)
            self.pointMoved.emit(self.control_point)
            self.update()

    def mouseReleaseEvent(self, event):
        self.dragging = False

    def set_point_position(self, x, y):
        """Set the control point position programmatically using normalized coordinates (-1 to 1)."""
        # Clamp to -1 to 1 range
        x = max(-1.0, min(x, 1.0))
        y = max(-1.0, min(y, 1.0))

        self.control_point = QtCore.QPointF(x, y)
        self.update()

    def set_point_color(self, color):
        """Set the control point color."""
        if isinstance(color, str):
            self.point_color = QtGui.QColor(color)
        else:
            self.point_color = color
        self.update()


class SquareWidget(QtWidgets.QWidget):
    """A widget that maintains a square aspect ratio and maximizes size."""

    def __init__(self, child_widget, parent=None):
        super(SquareWidget, self).__init__(parent)
        self.child_widget = child_widget
        self.child_widget.setParent(self)
        self.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

    def resizeEvent(self, event):
        """Resize the child widget to maintain square aspect ratio."""
        super(SquareWidget, self).resizeEvent(event)

        # Get the available size
        size = self.size()
        width = size.width()
        height = size.height()

        # Calculate square size (use the smaller dimension)
        square_size = min(width, height)

        # Center the child widget
        x = (width - square_size) // 2
        y = (height - square_size) // 2

        # Resize and position the child widget
        self.child_widget.setGeometry(x, y, square_size, square_size)

    def sizeHint(self):
        """Return size hint from child widget."""
        return self.child_widget.sizeHint()

    def minimumSizeHint(self):
        """Return minimum size hint from child widget."""
        return self.child_widget.minimumSizeHint()


class CatchlightDetailsWidget(QtWidgets.QWidget):
    """Widget for catchlight details control with sliders, rotation panel, and edit button."""

    def __init__(self, parent=None):
        super(CatchlightDetailsWidget, self).__init__(parent)
        # Create shared API instance to avoid repeated instantiation
        self.catchlight_api = ParametricCatchlight()
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """Set up the UI components."""
        # Main horizontal layout
        main_layout = FramelessHLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)
        self.setLayout(main_layout)

        # Left section - Controls
        self.setup_left_controls(main_layout)

        # Middle section - Rotation panel
        self.setup_rotation_panel(main_layout)

        # Right section - Edit button and material controls
        self.setup_right_section(main_layout)

    def setup_left_controls(self, parent_layout):
        """Set up the left controls section."""
        left_container = QtWidgets.QWidget()
        left_container.setStyleSheet("background-color: transparent;")
        left_layout = FramelessVLayout()
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(10)
        left_container.setLayout(left_layout)

        # Align eye button
        self.align_eye_button = MToolButton()
        self.align_eye_button.setText("对齐眼球")
        self.align_eye_button.setFixedHeight(50)
        self.align_eye_button.setToolButtonStyle(QtCore.Qt.ToolButtonTextOnly)  # Show text only

        # Apply custom style to match the auto match button
        self.align_eye_button.setStyleSheet(
            """
            QToolButton {
                border: 3px solid #3E3E3E;
                border-radius: 8px;
                background-color: #3A3A3A;
                color: #CCCCCC;
                padding: 3px;
            }
            QToolButton:checked {
                background-color: #1E90FF;  /* Bright blue when checked */
                color: white;
                font-weight: bold;
            }
            QToolButton:hover {
                border-color: #5E5E5E;
                background-color: #4A4A4A;
            }
            QToolButton:pressed {
                background-color: #1A1A1A;
            }
        """
        )
        left_layout.addWidget(self.align_eye_button)

        # Size slider
        size_layout = FramelessHLayout()
        size_label = MLabel("大小: ")
        size_label.setAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        size_label.setStyleSheet("background-color: transparent; border: none;")
        self.size_slider = MSlider(QtCore.Qt.Horizontal)
        self.size_slider.setStyleSheet(
            "background-color: transparent; border: 1px solid #666666; border-radius: 4px; padding: 2px;"
        )
        self.size_slider.setRange(0, 100)
        self.size_slider.setValue(50)
        size_layout.addWidget(size_label)
        size_layout.addWidget(self.size_slider)
        left_layout.addLayout(size_layout)

        # Angle slider
        angle_layout = FramelessHLayout()
        angle_label = MLabel("角度: ")
        angle_label.setAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        angle_label.setStyleSheet("background-color: transparent; border: none;")
        self.angle_slider = MSlider(QtCore.Qt.Horizontal)
        self.angle_slider.setStyleSheet(
            "background-color: transparent; border: 1px solid #666666; border-radius: 4px; padding: 2px;"
        )
        self.angle_slider.setRange(0, 360)
        self.angle_slider.setValue(0)
        angle_layout.addWidget(angle_label)
        angle_layout.addWidget(self.angle_slider)
        left_layout.addLayout(angle_layout)

        # Distance slider
        distance_layout = FramelessHLayout()
        distance_label = MLabel("距离: ")
        distance_label.setAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        distance_label.setStyleSheet("background-color: transparent; border: none;")
        self.distance_slider = MSlider(QtCore.Qt.Horizontal)
        self.distance_slider.setStyleSheet(
            "background-color: transparent; border: 1px solid #666666; border-radius: 4px; padding: 2px;"
        )
        self.distance_slider.setRange(0, 100)
        self.distance_slider.setValue(33)
        distance_layout.addWidget(distance_label)
        distance_layout.addWidget(self.distance_slider)
        left_layout.addLayout(distance_layout)

        parent_layout.addWidget(left_container, 2)

    def setup_rotation_panel(self, parent_layout):
        """Set up the rotation panel section."""
        rotation_container = QtWidgets.QWidget()
        rotation_container.setStyleSheet("background-color: transparent;")
        rotation_layout = FramelessVLayout()
        rotation_layout.setContentsMargins(5, 5, 5, 5)
        rotation_layout.setSpacing(5)
        rotation_container.setLayout(rotation_layout)

        # Rotation panel label
        rotation_label = MLabel("旋转面板")
        rotation_label.setAlignment(QtCore.Qt.AlignCenter)
        rotation_label.setStyleSheet("background-color: transparent; border: none; font-weight: bold;")
        rotation_layout.addWidget(rotation_label)

        # Rotation widget wrapped in square container
        self.rotation_widget = CatchlightRotateWidget()
        self.rotation_widget.setStyleSheet("background-color: transparent;")

        # Wrap in square widget to maintain 1:1 aspect ratio while maximizing size
        self.square_container = SquareWidget(self.rotation_widget)
        rotation_layout.addWidget(self.square_container, 1)  # Give it stretch factor to expand

        parent_layout.addWidget(rotation_container, 1)

    def setup_right_section(self, parent_layout):
        """Set up the right section with edit button and material controls."""
        # Create horizontal layout for edit button and material controls
        right_section_layout = FramelessHLayout()
        right_section_layout.setSpacing(10)

        # Left part - Edit shape button
        self.setup_edit_button(right_section_layout)

        # Vertical separator line
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.VLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        separator.setStyleSheet("color: #666666;")
        separator.setFixedWidth(2)
        right_section_layout.addWidget(separator)

        # Right part - Material controls
        self.setup_material_controls(right_section_layout)

        parent_layout.addLayout(right_section_layout, 2)

    def setup_edit_button(self, parent_layout):
        """Set up the edit shape buttons."""
        edit_button_layout = FramelessVLayout()
        edit_button_layout.setSpacing(10)  # Add spacing between buttons

        # Edit shape label
        edit_label = MLabel("编辑:")
        edit_label.setAlignment(QtCore.Qt.AlignCenter)
        edit_label.setStyleSheet("background-color: transparent; border: none; font-weight: bold;")
        edit_button_layout.addWidget(edit_label)

        # Common button style
        button_style = """
            QPushButton {
                padding: 8px 4px;
                text-align: center;
                border: 2px solid #4A4A4A;
                border-radius: 6px;
                background-color: #3A3A3A;
                color: #CCCCCC;
            }
            QPushButton:hover {
                border-color: #5E5E5E;
                background-color: #4A4A4A;
            }
            QPushButton:pressed {
                background-color: #1A1A1A;
            }
        """

        # Vertex edit button (original functionality)
        self.vertex_edit_button = QtWidgets.QPushButton("点")
        self.vertex_edit_button.setFixedWidth(60)
        self.vertex_edit_button.setStyleSheet(button_style)
        self.vertex_edit_button.setSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        edit_button_layout.addWidget(self.vertex_edit_button)

        # Edge edit button
        self.edge_edit_button = QtWidgets.QPushButton("线")
        self.edge_edit_button.setFixedWidth(60)
        self.edge_edit_button.setStyleSheet(button_style)
        self.edge_edit_button.setSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        edit_button_layout.addWidget(self.edge_edit_button)

        # Face edit button
        self.face_edit_button = QtWidgets.QPushButton("面")
        self.face_edit_button.setFixedWidth(60)
        self.face_edit_button.setStyleSheet(button_style)
        self.face_edit_button.setSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        edit_button_layout.addWidget(self.face_edit_button)

        parent_layout.addLayout(edit_button_layout)

    def setup_material_controls(self, parent_layout):
        """Set up the material controls section."""
        material_container = QtWidgets.QWidget()
        material_container.setStyleSheet("background-color: transparent;")
        material_layout = FramelessVLayout()
        material_layout.setContentsMargins(5, 5, 5, 5)
        material_layout.setSpacing(15)
        material_container.setLayout(material_layout)

        # Material section label
        material_label = MLabel("材质属性: ")
        material_label.setAlignment(QtCore.Qt.AlignHCenter)
        material_label.setStyleSheet("background-color: transparent; border: none; font-weight: bold;")
        material_layout.addWidget(material_label)

        # Color picker
        color_layout = FramelessHLayout()
        color_layout.setSpacing(8)  # Consistent spacing
        color_label = MLabel("颜色: ")
        color_label.setAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        color_label.setStyleSheet("background-color: transparent; border: none;")
        color_label.setFixedWidth(90)  # Fixed width for alignment

        # Color picker button
        self.color_picker_button = QtWidgets.QPushButton()
        self.color_picker_button.setFixedSize(120, 30)  # Increased height for better alignment
        self.color_picker_button.setStyleSheet(
            """
            QPushButton {
                background-color: #FFFFFF;
                border: 2px solid #666666;
                border-radius: 4px;
            }
            QPushButton:hover {
                border-color: #888888;
            }
            QPushButton:pressed {
                border-color: #AAAAAA;
            }
        """
        )
        current_color = self.catchlight_api.get_catchlight_color()
        self.current_color = (
            QtGui.QColor(
                current_color[0] * 255,
                current_color[1] * 255,
                current_color[2] * 255,
            )
            if current_color
            else QtGui.QColor(255, 255, 255)
        )
        self.update_color_button_style(self.current_color)

        color_layout.addWidget(color_label)
        color_layout.addWidget(self.color_picker_button)
        color_layout.addStretch()
        material_layout.addLayout(color_layout)

        # Opacity slider
        opacity_layout = FramelessHLayout()
        opacity_layout.setSpacing(8)  # Consistent spacing
        opacity_label = MLabel("不透明度: ")
        opacity_label.setAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        opacity_label.setStyleSheet("background-color: transparent; border: none;")
        opacity_label.setFixedWidth(90)  # Fixed width for alignment
        self.opacity_slider = MSlider(QtCore.Qt.Horizontal)
        self.opacity_slider.setStyleSheet(
            "background-color: transparent; border: 1px solid #666666; border-radius: 4px; padding: 2px;"
        )
        self.opacity_slider.setRange(0, 100)
        opacity = self.catchlight_api.get_catchlight_opacity()
        opacity_value = int(opacity * 100)
        self.opacity_slider.setValue(opacity_value)
        self.opacity_slider.setFixedHeight(30)  # Fixed height for alignment
        opacity_layout.addWidget(opacity_label)
        opacity_layout.addWidget(self.opacity_slider)
        material_layout.addLayout(opacity_layout)

        # Add stretch to push controls to the top
        material_layout.addStretch()

        parent_layout.addWidget(material_container)

    def connect_signals(self):
        """Connect signals to slots."""
        self.align_eye_button.clicked.connect(self.slot_align_eye_clicked)
        self.size_slider.valueChanged.connect(self.slot_size_changed)
        self.angle_slider.valueChanged.connect(self.slot_angle_changed)
        self.distance_slider.valueChanged.connect(self.slot_distance_changed)
        self.rotation_widget.pointMoved.connect(self.slot_rotation_changed)
        # Connect edit buttons
        self.vertex_edit_button.clicked.connect(self.slot_vertex_edit_clicked)
        self.edge_edit_button.clicked.connect(self.slot_edge_edit_clicked)
        self.face_edit_button.clicked.connect(self.slot_face_edit_clicked)

        # Material controls signals
        self.color_picker_button.clicked.connect(self.slot_color_picker_clicked)
        self.opacity_slider.valueChanged.connect(self.slot_opacity_changed)

    def get_selected_catchlight_names(self):
        """Get selected catchlight names from the parent widget's eye light list.

        Returns:
            list: List of selected catchlight names
        """
        # Find the parent CatchlightWidget and get selected items from its eye_light_list
        parent_widget = self.parent()
        while parent_widget and not hasattr(parent_widget, "eye_light_list"):
            parent_widget = parent_widget.parent()

        if parent_widget and hasattr(parent_widget, "eye_light_list"):
            selected_items = parent_widget.eye_light_list.selectedItems()
            # Get full name from short name using the new method
            selected_names = []
            for item in selected_items:
                item_name = parent_widget.eye_light_list.get_item_name(item)
                if item_name:
                    selected_names.append(self.catchlight_api.get_catchlight_full_name(item_name))
            return selected_names

        return []

    @QtCore.Slot()
    def slot_align_eye_clicked(self):
        """Handle align eye button click."""
        # Get selected catchlight names from list
        selected_catchlight_names = self.get_selected_catchlight_names()
        if selected_catchlight_names:
            self.catchlight_api.align_catchlights_to_eyes(selected_catchlight_names)
        else:
            MMessage.warning("未选择眼神光", parent=self)

    @QtCore.Slot()
    def slot_reset_details_clicked(self):
        """Handle reset details button click (center align functionality)."""
        MMessage.info("重置详情按钮已点击 - 居中对齐功能", parent=self)

    @QtCore.Slot(int)
    def slot_size_changed(self, value):
        """Handle size slider change."""
        scale_factor = value / 50.0
        selected_catchlight_names = self.get_selected_catchlight_names()
        if not selected_catchlight_names:
            MMessage.warning("未选择眼神光", parent=self)
        else:
            for selected_catchlight_name in selected_catchlight_names:
                self.catchlight_api.resize_catchlights(selected_catchlight_name, scale_factor)

    @QtCore.Slot(int)
    def slot_angle_changed(self, value):
        """Handle angle slider change."""
        angle_degree = value

        selected_catchlight_names = self.get_selected_catchlight_names()
        if not selected_catchlight_names:
            MMessage.warning("未选择眼神光", parent=self)
        else:
            for selected_catchlight_name in selected_catchlight_names:
                self.catchlight_api.rotate_catchlights(selected_catchlight_name, angle_degree)

    @QtCore.Slot(int)
    def slot_distance_changed(self, value):
        """Handle distance slider change."""
        distance = value / 333.3 - 0.1  # Map 0-100 to -0.1 to 0.2
        selected_catchlight_names = self.get_selected_catchlight_names()
        if not selected_catchlight_names:
            MMessage.warning("未选择眼神光", parent=self)
        else:
            for selected_catchlight_name in selected_catchlight_names:
                self.catchlight_api.offset_catchlights(selected_catchlight_name, distance)

    @QtCore.Slot(QtCore.QPointF)
    def slot_rotation_changed(self, point):
        """Handle rotation panel change."""
        selected_catchlight_names = self.get_selected_catchlight_names()
        if not selected_catchlight_names:
            MMessage.warning("未选择眼神光", parent=self)
        else:
            for selected_catchlight_name in selected_catchlight_names:
                self.catchlight_api.revolve_catchlights(selected_catchlight_name, point.x() * 0.5, point.y() * 0.5)

    @QtCore.Slot()
    def slot_vertex_edit_clicked(self):
        """Handle vertex edit button click (original edit shape functionality)."""
        selected_catchlight_names = self.get_selected_catchlight_names()
        if selected_catchlight_names:
            self.catchlight_api.enter_edit_mode(selected_catchlight_names[0], "vertex")
        else:
            MMessage.warning("未选择眼神光", parent=self)

    @QtCore.Slot()
    def slot_edge_edit_clicked(self):
        """Handle edge edit button click."""
        selected_catchlight_names = self.get_selected_catchlight_names()
        if selected_catchlight_names:
            self.catchlight_api.enter_edit_mode(selected_catchlight_names[0], "edge")
            MMessage.info("进入线编辑模式", parent=self)
        else:
            MMessage.warning("未选择眼神光", parent=self)

    @QtCore.Slot()
    def slot_face_edit_clicked(self):
        """Handle face edit button click."""
        selected_catchlight_names = self.get_selected_catchlight_names()
        if selected_catchlight_names:
            self.catchlight_api.enter_edit_mode(selected_catchlight_names[0], "face")
            MMessage.info("进入面编辑模式", parent=self)
        else:
            MMessage.warning("未选择眼神光", parent=self)

    @QtCore.Slot()
    def slot_color_picker_clicked(self):
        """Handle color picker button click with real-time preview."""
        # Get current color from Maya
        try:
            current_maya_color = self.catchlight_api.get_catchlight_color()
            if current_maya_color:
                # Convert Maya color (0.0-1.0) to QColor (0-255)
                r = int(current_maya_color[0] * 255)
                g = int(current_maya_color[1] * 255)
                b = int(current_maya_color[2] * 255)
                self.current_color = QtGui.QColor(r, g, b)
        except Exception as e:
            logging.error(f"Failed to get current color from Maya: {e}")
            # Use default color if failed
            self.current_color = QtGui.QColor(255, 255, 255)

        # Store original color for cancel operation
        original_color = QtGui.QColor(self.current_color)

        # Create color dialog with custom options
        color_dialog = QtWidgets.QColorDialog(self.current_color, self)
        color_dialog.setWindowTitle("选择眼神光颜色")
        color_dialog.setOption(QtWidgets.QColorDialog.ShowAlphaChannel, False)

        # Connect real-time preview signal
        color_dialog.currentColorChanged.connect(self.slot_color_preview)

        # Show dialog and handle result
        result = color_dialog.exec_()

        if result == QtWidgets.QDialog.Accepted:
            # User clicked OK - apply the final color
            final_color = color_dialog.currentColor()
            if final_color.isValid():
                self.current_color = final_color
                self.update_color_button_style(final_color)
                self.catchlight_api.set_catchlight_color(final_color)
                MMessage.info(f"眼神光颜色已更改为: {final_color.name()}", parent=self)
        else:
            # User clicked Cancel - restore original color
            self.current_color = original_color
            self.update_color_button_style(original_color)
            self.catchlight_api.set_catchlight_color(original_color)
            MMessage.info("颜色更改已取消", parent=self)

    @QtCore.Slot(QtGui.QColor)
    def slot_color_preview(self, color):
        """Handle real-time color preview during color selection."""
        if color.isValid():
            # Apply color immediately for preview
            self.catchlight_api.set_catchlight_color(color)
            # Update button style for visual feedback
            self.update_color_button_style(color)

    def update_color_button_style(self, color):
        """Update the color picker button style with the given color."""
        self.color_picker_button.setStyleSheet(
            f"""
            QPushButton {{
                background-color: {color.name()};
                border: 2px solid #666666;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border-color: #888888;
            }}
            QPushButton:pressed {{
                border-color: #AAAAAA;
            }}
        """
        )

    @QtCore.Slot(int)
    def slot_opacity_changed(self, value):
        """Handle opacity slider change."""
        opacity = value / 100.0  # Convert to 0.0-1.0 range
        self.catchlight_api.set_catchlight_opacity(opacity)

    def update_ui_from_catchlight_state(self, catchlight_name):
        """Update UI controls based on current catchlight state.

        Args:
            catchlight_name (str): Name of the catchlight to get state from
        """
        if not catchlight_name:
            return

        try:
            catchlight_full_name = self.catchlight_api.get_catchlight_full_name(catchlight_name)

            # Get current catchlight state
            current_size = self.catchlight_api.get_catchlight_size(catchlight_full_name)
            current_rotation = self.catchlight_api.get_catchlight_rotation(catchlight_full_name)
            current_offset = self.catchlight_api.get_catchlight_offset(catchlight_full_name)
            current_revolution = self.catchlight_api.get_catchlight_revolution(catchlight_full_name)

            # Update size slider (scale_factor to slider value: value = scale_factor * 50)
            size_value = int(current_size * 50)
            size_value = max(0, min(100, size_value))  # Clamp to 0-100
            self.size_slider.setValue(size_value)

            # Update angle slider (0-360 degrees)
            angle_value = int(current_rotation)
            angle_value = max(0, min(360, angle_value))  # Clamp to 0-360
            self.angle_slider.setValue(angle_value)

            # Update distance slider (offset to slider value: value = (offset + 0.1) * 333.3)
            distance_value = int((current_offset + 0.1) * 333.3)
            distance_value = max(0, min(100, distance_value))  # Clamp to 0-100
            self.distance_slider.setValue(distance_value)

            # Update rotation panel control point (revolution to normalized coordinates)
            degree_up, degree_right = current_revolution
            # Convert from revolution degrees to normalized coordinates (-1 to 1)
            # Scale factor: revolution uses 0.5 multiplier, so reverse it
            normalized_x = degree_right / 0.5 if degree_right != 0 else 0.0
            normalized_y = degree_up / 0.5 if degree_up != 0 else 0.0
            # Clamp to -1 to 1 range
            normalized_x = max(-1.0, min(1.0, normalized_x))
            normalized_y = max(-1.0, min(1.0, normalized_y))

            # Update rotation widget control point
            self.rotation_widget.control_point = QtCore.QPointF(normalized_y, normalized_x)
            self.rotation_widget.update()  # Trigger repaint

        except Exception as e:
            logging.error(f"Error updating UI from catchlight state: {e}")


class CatchlightItemDelegate(QtWidgets.QStyledItemDelegate):
    """
    Delegate for rendering eye light items in the view.
    """

    def __init__(self, parent=None):
        super(CatchlightItemDelegate, self).__init__(parent)
        self._spacing = ITEM_SPACING
        self._thumbnail_size = THUMBNAIL_SIZE
        self._text_height = TEXT_HEIGHT

    def paint(self, painter, option, index):
        """
        Paint the eye light item.
        """
        # Get data from model
        icon = index.data(QtCore.Qt.DecorationRole)
        text = index.data(QtCore.Qt.DisplayRole)

        # Calculate rectangles
        rect = option.rect
        thumbnail_rect = QtCore.QRect(
            rect.x() + (rect.width() - self._thumbnail_size) // 2,
            rect.y() + self._spacing,
            self._thumbnail_size,
            self._thumbnail_size,
        )
        text_rect = QtCore.QRect(
            rect.x() + self._spacing,
            rect.y() + self._thumbnail_size + self._spacing,
            rect.width() - self._spacing * 2,
            self._text_height,
        )

        # Draw selection background if selected
        if option.state & QtWidgets.QStyle.State_Selected:
            painter.fillRect(rect, QtGui.QColor("#494949"))
        elif option.state & QtWidgets.QStyle.State_MouseOver:
            painter.fillRect(rect, QtGui.QColor("#3a3a3a"))

        # Draw thumbnail placeholder
        if icon:
            if isinstance(icon, QtGui.QIcon):
                pixmap = icon.pixmap(self._thumbnail_size, self._thumbnail_size)
                painter.drawPixmap(thumbnail_rect, pixmap)
        else:
            # Draw placeholder rectangle
            painter.fillRect(thumbnail_rect, QtGui.QColor("#555555"))
            painter.setPen(QtGui.QPen(QtGui.QColor("#888888")))
            painter.drawRect(thumbnail_rect)

        # Draw text
        if text:
            painter.setPen(QtGui.QPen(QtGui.QColor("#ffffff")))
            font = painter.font()
            font.setPointSize(9)
            painter.setFont(font)
            painter.drawText(text_rect, QtCore.Qt.AlignCenter, text)

    def sizeHint(self, option, index):
        """
        Return the size of the item.
        """
        width = self._thumbnail_size + self._spacing * 2
        height = self._thumbnail_size + self._text_height + self._spacing * 2
        return QtCore.QSize(width, height)


class CatchlightItemView(QtWidgets.QListView):
    """
    Simplified item view for eye light presets.
    """

    sig_item_selected = QtCore.Signal(dict)

    def __init__(self, parent=None):
        super(CatchlightItemView, self).__init__(parent)
        self.import_button = None
        # Create shared API instance
        self.catchlight_api = ParametricCatchlight()
        # Initialize drag start position for custom drag implementation
        self._drag_start_position = None
        self.setup_ui()

    def setup_ui(self):
        """Set up the UI."""
        # Set view properties
        self.setViewMode(QtWidgets.QListView.IconMode)
        self.setResizeMode(QtWidgets.QListView.Adjust)
        self.setMovement(QtWidgets.QListView.Static)
        self.setSpacing(ITEM_SPACING)
        self.setUniformItemSizes(True)
        self.setIconSize(QtCore.QSize(ICON_SIZE, ICON_SIZE))
        self.setGridSize(QtCore.QSize(GRID_WIDTH, GRID_HEIGHT))

        # Set selection properties
        self.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)

        # Disable Qt built-in drag and drop for custom implementation
        self.setDragEnabled(False)
        self.setAcceptDrops(False)

        # Set delegate
        self.setItemDelegate(CatchlightItemDelegate(self))

        # Set model with catchlight data
        self.setup_catchlight_data()

        # Connect signals
        self.clicked.connect(self.slot_item_clicked)

    def setup_catchlight_data(self):
        """Setup catchlight data from API."""
        model = QtGui.QStandardItemModel()

        # Get existing catchlights from API
        try:
            catchlight_names = self.catchlight_api.get_catchlight_presets()

            # Hide all catchlights
            self.catchlight_api.hide_catchlights(catchlight_names)

            # Extract just the name from full path if needed
            for catchlight_full_name in catchlight_names:
                # Show current catchlight
                cmds.setAttr(catchlight_full_name + ".v", 1)
                # Get the short name from the full path
                catchlight_name = self.catchlight_api.get_catchlight_short_name(catchlight_full_name)
                icon_path = self.catchlight_api.capture_catchlight_thumbnail(catchlight_name)
                # Hide current catchlight after capturing thumbnail
                cmds.setAttr(catchlight_full_name + ".v", 0)

                item = QtGui.QStandardItem(catchlight_name)

                # Set icon from the PNG thumbnail path
                if icon_path and os.path.exists(icon_path):
                    icon = QtGui.QIcon(icon_path)
                    item.setIcon(icon)

                model.appendRow(item)

            # Reset visibility of all catchlights
            self.catchlight_api.reset_catchlights_visibility(catchlight_names)

        except Exception as e:
            MMessage.error(f"加载眼神光时出错: {e}", parent=self)

        self.setModel(model)

        # Show import button if no catchlights exist
        self.update_import_button_visibility()

    def update_import_button_visibility(self):
        """Show or hide import button based on whether catchlights exist."""
        has_items = self.model() and self.model().rowCount() > 0

        if not has_items:
            self.show_import_button()
        else:
            self.hide_import_button()

    def show_import_button(self):
        """Show the import catchlight button."""
        if not self.import_button:
            self.import_button = dayu_widgets.MPushButton("初始化预设")
            self.import_button.setParent(self)
            self.import_button.setStyleSheet(
                """
                MPushButton {
                    background-color: #1890FF;
                    color: #ffffff;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: bold;
                    min-width: 120px;
                }
                MPushButton:hover {
                    background-color: #40A9FF;
                }
                MPushButton:pressed {
                    background-color: #096DD9;
                }
            """
            )
            # Connect button signal (functionality to be implemented later)
            self.import_button.clicked.connect(self.slot_import_catchlights)

        self.import_button.show()
        self.position_import_button()

    def hide_import_button(self):
        """Hide the import catchlight button."""
        if self.import_button:
            self.import_button.hide()

    def position_import_button(self):
        """Position the import button in the center of the view."""
        if self.import_button:
            button_size = self.import_button.sizeHint()
            view_rect = self.rect()
            x = (view_rect.width() - button_size.width()) // 2
            y = (view_rect.height() - button_size.height()) // 2
            self.import_button.move(x, y)

    def resizeEvent(self, event):
        """Handle resize event to reposition import button."""
        super(CatchlightItemView, self).resizeEvent(event)
        self.position_import_button()

    @QtCore.Slot()
    def slot_import_catchlights(self):
        """Handle import catchlights button click (to be implemented)."""
        self.catchlight_api.init_catchlight_presets()
        self.setup_catchlight_data()

    @QtCore.Slot(QtCore.QModelIndex)
    def slot_item_clicked(self, index):
        """Handle item click."""
        item_name = index.data(QtCore.Qt.DisplayRole)
        if item_name:
            item_data = {"name": item_name}
            self.sig_item_selected.emit(item_data)

    def mousePressEvent(self, event):
        """Handle mouse press events for custom drag implementation."""
        if event.button() == QtCore.Qt.LeftButton:
            self._drag_start_position = event.pos()
        super(CatchlightItemView, self).mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """Handle mouse move events for custom drag implementation."""
        if not (event.buttons() & QtCore.Qt.LeftButton):
            return

        if not self._drag_start_position:
            return

        # Check if the distance is large enough to start a drag
        distance = (event.pos() - self._drag_start_position).manhattanLength()
        if distance < QtWidgets.QApplication.startDragDistance():
            return

        # Pre-flight checks to prevent crashes
        if not self._is_safe_to_drag():
            logging.warning("Drag operation aborted: unsafe conditions detected")
            return

        # Get the item at the drag start position
        index = self.indexAt(self._drag_start_position)
        if not index.isValid():
            return

        # Get the selected item data
        item_name = index.data(QtCore.Qt.DisplayRole)

        if not item_name or not isinstance(item_name, str):
            logging.warning(f"Invalid item name for drag: {item_name}")
            return

        # Create drag object with error handling
        try:
            drag = QtGui.QDrag(self)
            if not drag:
                logging.error("Failed to create QDrag object")
                return

            mimeData = QtCore.QMimeData()
            if not mimeData:
                logging.error("Failed to create QMimeData object")
                return

            # Set the eye light data as text (primary format)
            mimeData.setText(item_name)

            # Set custom format for better compatibility (like makeup library)
            mimeData.setData("application/x-catchlight-item", QtCore.QByteArray(item_name.encode()))

            # No icon data needed for drag operation - simplified approach

            drag.setMimeData(mimeData)

            # Execute drag with copy action (no drag pixmap needed)
            drag.exec_(QtCore.Qt.CopyAction)

        except Exception as e:
            logging.error(f"Error during drag operation: {e}")
            import traceback
            traceback.print_exc()

    def _is_safe_to_drag(self):
        """Check if it's safe to start a drag operation."""
        try:
            # Check if widget is valid
            if not self or self.isHidden() or not self.isVisible():
                return False

            # Check if Maya is in a stable state
            try:
                import maya.cmds as cmds
                # Simple Maya command to test if Maya is responsive
                cmds.about(version=True)
            except Exception:
                logging.warning("Maya appears to be unresponsive")
                return False

            # Check if there are any pending Qt events that might conflict
            QtWidgets.QApplication.processEvents()

            return True

        except Exception as e:
            logging.error(f"Error checking drag safety: {e}")
            return False


class CatchlightItemWidget(QtWidgets.QWidget):
    """Custom widget for catchlight list items with left/right toggle buttons."""

    # Signals for button state changes
    leftButtonToggled = QtCore.Signal(str, bool)  # item_name, checked
    rightButtonToggled = QtCore.Signal(str, bool)  # item_name, checked

    def __init__(self, item_name, icon_data=None, parent=None):
        super(CatchlightItemWidget, self).__init__(parent)
        self.item_name = item_name
        # icon_data parameter kept for compatibility but not used
        self.setup_ui()

    def setup_ui(self):
        """Set up the UI components."""
        # Set fixed height - make it taller for better appearance
        self.setFixedHeight(LIST_ITEM_CONTENT_HEIGHT)

        # Make the widget background transparent to show selection highlight
        self.setStyleSheet("background-color: transparent;")

        # Create a container widget for perfect centering
        container = QtWidgets.QWidget()
        container.setStyleSheet("background-color: transparent;")

        # Main layout for the entire widget
        main_layout = FramelessVLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        self.setLayout(main_layout)

        # Add container to main layout with center alignment
        main_layout.addWidget(container, 0, QtCore.Qt.AlignVCenter)

        # Horizontal layout for content inside container
        layout = FramelessHLayout()
        layout.setContentsMargins(12, 0, 12, 0)  # Only horizontal margins
        layout.setSpacing(10)
        container.setLayout(layout)

        # Add simple icon placeholder
        icon_label = QtWidgets.QLabel()
        icon_label.setFixedSize(36, 36)
        icon_label.setAlignment(QtCore.Qt.AlignCenter)

        # Always show simple placeholder icon - no complex icon processing needed
        icon_label.setText("◯")
        icon_label.setStyleSheet("color: #888888; background-color: #444444; border-radius: 18px; font-size: 18px;")

        layout.addWidget(icon_label, 0, QtCore.Qt.AlignVCenter)

        # Item name label
        self.name_label = MLabel(self.item_name)
        self.name_label.setStyleSheet("color: #ffffff; background-color: transparent; font-weight: normal;")
        self.name_label.setAlignment(QtCore.Qt.AlignVCenter)
        # Make the label transparent for mouse events so clicks pass through to the list item
        self.name_label.setAttribute(QtCore.Qt.WA_TransparentForMouseEvents, True)
        layout.addWidget(self.name_label, 0, QtCore.Qt.AlignVCenter)

        # Add stretch to push buttons to the right
        layout.addStretch()

        # Left button
        self.left_button = MToolButton()
        self.left_button.setText("左")
        self.left_button.setCheckable(True)
        self.left_button.setChecked(True)  # Default checked
        self.left_button.setFixedSize(36, 36)  # Larger size for better visibility
        self.left_button.setStyleSheet(
            """
            MToolButton {
                background-color: #3A3A3A;
                border: 1px solid #5E5E5E;
                border-radius: 5px;
                color: #CCCCCC;
                text-align: center;
            }
            MToolButton:checked {
                background-color: #1890FF;
                border-color: #40A9FF;
                color: white;
            }
            MToolButton:hover {
                border-color: #7E7E7E;
            }
            MToolButton:pressed {
                background-color: #2A2A2A;
            }
        """
        )

        # Right button
        self.right_button = MToolButton()
        self.right_button.setText("右")
        self.right_button.setCheckable(True)
        self.right_button.setChecked(True)  # Default checked
        self.right_button.setFixedSize(36, 36)  # Larger size for better visibility
        self.right_button.setStyleSheet(
            """
            MToolButton {
                background-color: #3A3A3A;
                border: 1px solid #5E5E5E;
                border-radius: 5px;
                color: #CCCCCC;
                text-align: center;
            }
            MToolButton:checked {
                background-color: #1890FF;
                border-color: #40A9FF;
                color: white;
            }
            MToolButton:hover {
                border-color: #7E7E7E;
            }
            MToolButton:pressed {
                background-color: #2A2A2A;
            }
        """
        )

        layout.addWidget(self.right_button, 0, QtCore.Qt.AlignVCenter)
        layout.addWidget(self.left_button, 0, QtCore.Qt.AlignVCenter)

        # Connect signals
        self.left_button.toggled.connect(lambda checked: self.leftButtonToggled.emit(self.item_name, checked))
        self.right_button.toggled.connect(lambda checked: self.rightButtonToggled.emit(self.item_name, checked))

    def get_item_name(self):
        """Get the item name."""
        return self.item_name

    def set_left_checked(self, checked):
        """Set left button checked state."""
        self.left_button.setChecked(checked)

    def set_right_checked(self, checked):
        """Set right button checked state."""
        self.right_button.setChecked(checked)

    def is_left_checked(self):
        """Get left button checked state."""
        return self.left_button.isChecked()

    def is_right_checked(self):
        """Get right button checked state."""
        return self.right_button.isChecked()


class CatchlightListWidget(QtWidgets.QListWidget):
    """
    List widget for eye light parameters or properties.
    """

    def __init__(self, parent=None):
        super(CatchlightListWidget, self).__init__(parent)
        # Create shared API instance
        self.catchlight_api = ParametricCatchlight()
        self.setup_ui()

    def setup_ui(self):
        """Set up the UI."""
        # Set basic properties
        self.setAlternatingRowColors(True)
        self.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)

        # Enable drag and drop
        self.setAcceptDrops(True)
        self.setDragDropMode(QtWidgets.QAbstractItemView.DropOnly)

        # Enable context menu
        self.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

        # Note: Selection change handling is done by parent CatchlightWidget

        # Set stylesheet with enhanced selection effects for custom widgets
        self.setStyleSheet(
            f"""
            QListWidget {{
                background-color: #2a2a2a;
                border: 1px solid #444444;
                border-radius: 4px;
                padding: 5px;
            }}
            QListWidget::item {{
                background-color: #333333;
                border-radius: 3px;
                margin: 3px 0px;
                padding: 0px;
                border: 2px solid transparent;
                min-height: {LIST_ITEM_HEIGHT}px;
                max-height: {LIST_ITEM_HEIGHT}px;
            }}
            QListWidget::item:selected {{
                background-color: #1890FF;
                border: 2px solid #40A9FF;
            }}
            QListWidget::item:hover {{
                background-color: #3a3a3a;
                border: 2px solid #666666;
            }}
            QListWidget::item:selected:hover {{
                background-color: #40A9FF;
                border: 2px solid #69C0FF;
            }}
        """
        )

    def paintEvent(self, event):
        """Override paint event to show placeholder text when empty."""
        super(CatchlightListWidget, self).paintEvent(event)

        # Show placeholder text when list is empty
        if self.count() == 0:
            painter = QtGui.QPainter(self.viewport())
            painter.setPen(QtGui.QColor("#888888"))
            font = painter.font()
            font.setPointSize(12)
            painter.setFont(font)

            rect = self.viewport().rect()
            painter.drawText(rect, QtCore.Qt.AlignCenter, "拖拽眼神光至此")

    def get_item_name(self, item):
        """Get the name of an item from UserRole data."""
        if item:
            return item.data(QtCore.Qt.UserRole)
        return None

    def dragEnterEvent(self, event):
        """Handle drag enter event."""
        # Accept any drag event - we'll check the data in dropEvent
        event.acceptProposedAction()

    def dragMoveEvent(self, event):
        """Handle drag move event."""
        # Accept any drag move event
        event.acceptProposedAction()

    def add_existing_catchlight_item(self, eye_light_name):
        """Add an existing catchlight item to the list without creating a new one.

        Args:
            eye_light_name (str): Name of the existing eye light

        Returns:
            str: The name of the added catchlight
        """
        # Extract just the name from full path if needed
        catchlight_name = self.catchlight_api.get_catchlight_short_name(eye_light_name)

        # Create list widget item
        item = QtWidgets.QListWidgetItem()
        item.setData(QtCore.Qt.UserRole, catchlight_name)  # Store the name in UserRole
        item.setData(QtCore.Qt.DisplayRole, "")  # Clear the display text to avoid duplication

        # Create custom widget for the item (no icon needed)
        item_widget = CatchlightItemWidget(catchlight_name, icon_data=None)
        # Set initial button states based on catchlight visibility
        (catchlight_visibility_left, catchlight_visibility_right) = self.catchlight_api.get_catchlight_visibility(
            eye_light_name
        )
        item_widget.set_left_checked(catchlight_visibility_left)
        item_widget.set_right_checked(catchlight_visibility_right)
        # Connect signals
        item_widget.leftButtonToggled.connect(self.slot_left_button_toggled)
        item_widget.rightButtonToggled.connect(self.slot_right_button_toggled)

        # Add the item to the list
        self.addItem(item)

        # Set the custom widget for this item
        item.setSizeHint(QtCore.QSize(item_widget.sizeHint().width(), LIST_ITEM_HEIGHT))
        self.setItemWidget(item, item_widget)

        # Update the display to remove placeholder text
        self.update()

        return catchlight_name

    def create_catchlight_item(self, eye_light_name, icon_data=None):
        """Add a catchlight item to the list with crash protection.

        Args:
            eye_light_name (str): Name of the eye light
            icon_data (QByteArray, optional): Unused, kept for compatibility

        Returns:
            str: The actual name of the created catchlight
        """
        created_base_name = None
        item = None
        item_widget = None

        try:
            # Validate inputs
            if not eye_light_name or not isinstance(eye_light_name, str):
                logging.error(f"Invalid eye_light_name: {eye_light_name}")
                return None

            # Check if widget is still valid
            if not self or self.isHidden():
                logging.warning("Widget is no longer valid")
                return None

            # Create the catchlight with crash protection
            try:
                api_instance = ParametricCatchlight()
                created_base_name = api_instance.create_catchlight(eye_light_name)
            except Exception as api_error:
                logging.error(f"API error creating catchlight: {api_error}")
                return None

            if not created_base_name:
                logging.error(f"Failed to create catchlight for: {eye_light_name}")
                return None

            # Create list widget item with error handling
            try:
                item = QtWidgets.QListWidgetItem()
                item.setData(QtCore.Qt.UserRole, created_base_name)
                item.setData(QtCore.Qt.DisplayRole, "")
            except Exception as item_error:
                logging.error(f"Error creating list item: {item_error}")
                return None

            # Create custom widget with error handling
            try:
                item_widget = CatchlightItemWidget(created_base_name, icon_data=icon_data)
                item_widget.leftButtonToggled.connect(self.slot_left_button_toggled)
                item_widget.rightButtonToggled.connect(self.slot_right_button_toggled)
            except Exception as widget_error:
                logging.error(f"Error creating item widget: {widget_error}")
                return None

            # Add to list with error handling
            try:
                self.addItem(item)
                item.setSizeHint(QtCore.QSize(item_widget.sizeHint().width(), LIST_ITEM_HEIGHT))
                self.setItemWidget(item, item_widget)

                # Auto-select the newly added item
                self.setCurrentItem(item)

                # Update the display
                self.update()

            except Exception as list_error:
                logging.error(f"Error adding item to list: {list_error}")
                # Clean up if adding failed
                if item:
                    try:
                        row = self.row(item)
                        if row >= 0:
                            self.takeItem(row)
                    except:
                        pass
                return None

            return created_base_name

        except Exception as e:
            logging.error(f"Critical error creating catchlight item: {e}")
            import traceback
            traceback.print_exc()

            # Clean up any partially created objects
            try:
                if item and self:
                    row = self.row(item)
                    if row >= 0:
                        self.takeItem(row)
            except:
                pass

            return None

    def dropEvent(self, event):
        """Handle drop event with async processing to prevent Maya freezing."""
        eye_light_name = None
        icon_data = None

        try:
            # Try to get data from custom format first (like makeup library approach)
            if event.mimeData().hasFormat("application/x-catchlight-item"):
                try:
                    data = event.mimeData().data("application/x-catchlight-item").data().decode()
                    eye_light_name = data
                except Exception as e:
                    logging.error(f"Error parsing custom format data: {e}")

            # If custom format failed, try text format
            if not eye_light_name and event.mimeData().hasText():
                try:
                    eye_light_name = event.mimeData().text()
                except Exception as e:
                    logging.error(f"Error parsing text data: {e}")

            # No icon processing needed - simplified approach
            icon_data = None

            # Process the data if we got it
            if eye_light_name:
                # Accept the drop immediately to prevent hanging
                event.acceptProposedAction()

                # Process the catchlight creation asynchronously using QTimer
                QtCore.QTimer.singleShot(0, lambda: self._async_create_catchlight(eye_light_name, icon_data))
            else:
                logging.warning("No valid catchlight data found in drop event")
                event.ignore()

        except Exception as e:
            logging.error(f"Error during drop event: {e}")
            event.ignore()

    def _async_create_catchlight(self, eye_light_name, icon_data):
        """Asynchronously create catchlight to prevent UI freezing and crashes."""
        # Use Maya's executeDeferred to ensure thread safety
        try:
            import maya.utils
            maya.utils.executeDeferred(self._safe_create_catchlight, eye_light_name, icon_data)
        except ImportError:
            # Fallback if maya.utils is not available
            self._safe_create_catchlight(eye_light_name, icon_data)

    def _safe_create_catchlight(self, eye_light_name, icon_data):
        """Thread-safe catchlight creation with comprehensive error handling."""
        try:
            # Validate input parameters
            if not eye_light_name or not isinstance(eye_light_name, str):
                logging.error(f"Invalid eye_light_name: {eye_light_name}")
                return

            # Set processing cursor
            QtWidgets.QApplication.setOverrideCursor(QtCore.Qt.WaitCursor)

            try:
                # Add the catchlight item with timeout protection
                created_name = self.create_catchlight_item(eye_light_name, icon_data)

                if not created_name:
                    logging.error(f"Failed to create catchlight for: {eye_light_name}")
                    self._show_error_message(f"无法创建眼神光: {eye_light_name}")
                else:
                    logging.info(f"Successfully created catchlight: {created_name}")

            finally:
                # Always restore cursor
                QtWidgets.QApplication.restoreOverrideCursor()

        except Exception as e:
            logging.error(f"Critical error creating catchlight: {e}")
            import traceback
            traceback.print_exc()

            # Ensure cursor is restored even on critical error
            try:
                QtWidgets.QApplication.restoreOverrideCursor()
            except:
                pass

            # Show error message safely
            self._show_error_message(f"创建眼神光时发生错误: {str(e)}")

    def _show_error_message(self, message):
        """Safely show error message without causing crashes."""
        try:
            from dayu_widgets import MMessage
            # Use QTimer to ensure message is shown in main thread
            QtCore.QTimer.singleShot(100, lambda: MMessage.error(message, parent=self))
        except Exception:
            # Ultimate fallback - just log to console
            logging.error(f"Error message: {message}")

    def show_context_menu(self, position):
        """Show context menu at the given position."""
        # Get the item at the clicked position
        item = self.itemAt(position)
        if not item:
            return  # No item at this position, don't show menu

        # Create context menu
        context_menu = QtWidgets.QMenu(self)

        # Add "Delete Item" action
        delete_action = context_menu.addAction("删除该项")
        delete_action.triggered.connect(lambda: self.delete_item(item))

        # Show menu at global position
        global_pos = self.mapToGlobal(position)
        context_menu.exec_(global_pos)

    def delete_items(self, items):
        """Delete the specified items from the list."""
        if not items:
            return

        # Convert single item to list for uniform processing
        if not isinstance(items, list):
            items = [items]

        # Remove items from the list
        for item in items:
            if item:
                item_name = self.get_item_name(item)
                if item_name:
                    self.catchlight_api.delete_catchlight(item_name)

                row = self.row(item)
                self.takeItem(row)

        # Update the display to show placeholder text if list becomes empty
        self.update()

    def delete_item(self, item):
        """Delete the specified item from the list."""
        self.delete_items(item)

    @QtCore.Slot(str, bool)
    def slot_left_button_toggled(self, item_name, checked):
        """Handle left button toggle."""
        catchlight_full_name = self.catchlight_api.get_catchlight_full_name(item_name)
        self.catchlight_api.set_catchlight_visibility(catchlight_full_name, checked, "left")

    @QtCore.Slot(str, bool)
    def slot_right_button_toggled(self, item_name, checked):
        """Handle right button toggle."""
        catchlight_full_name = self.catchlight_api.get_catchlight_full_name(item_name)
        self.catchlight_api.set_catchlight_visibility(catchlight_full_name, checked, "right")


class CatchlightWidget(QtWidgets.QFrame):
    """
    Main widget for eye light management with three horizontal sections that can be resized.
    """

    def __init__(self, parent=None):
        super(CatchlightWidget, self).__init__(parent)
        # Create shared API instance
        self.catchlight_api = ParametricCatchlight()
        self.setup_ui()
        self.init_data()

    def setup_ui(self):
        """Initialize UI components."""
        self.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.main_layout = FramelessVLayout()
        self.main_layout.setContentsMargins(5, 5, 5, 5)
        self.setLayout(self.main_layout)

        # Create the three sub-widgets
        self.item_view_widget = self.create_item_view_widget()
        self.list_widget = self.create_list_widget()
        self.empty_widget = self.create_empty_widget()

        # Create a splitter to allow resizing the sections
        self.splitter = QtWidgets.QSplitter(QtCore.Qt.Horizontal)
        self.splitter.setChildrenCollapsible(False)  # Prevent sections from being collapsed
        self.splitter.setHandleWidth(8)  # Set the width of the splitter handle

        # Add the sub-widgets to the splitter
        self.splitter.addWidget(self.item_view_widget)
        self.splitter.addWidget(self.list_widget)
        self.splitter.addWidget(self.empty_widget)

        # Set initial sizes
        self.splitter.setSizes([338, 330, 1200])

        # Style the splitter handles
        self.style_splitter_handles()

        # Connect signals
        self.connect_signals()

        # Add the splitter to the main layout
        self.main_layout.addWidget(self.splitter)

    def init_data(self):
        existing_catchlights = self.catchlight_api.get_existing_catchlights()
        for catchlight in existing_catchlights:
            self.eye_light_list.add_existing_catchlight_item(catchlight)

    def create_item_view_widget(self):
        """Create the first section with item view."""
        # Create container
        container = QtWidgets.QFrame()
        container.setFrameShape(QtWidgets.QFrame.StyledPanel)
        container.setStyleSheet(
            """
            QFrame {
                background-color: #2a2a2a;
                border: 1px solid #444444;
                border-radius: 4px;
            }
        """
        )

        # Create layout
        layout = FramelessVLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        container.setLayout(layout)

        # Create title bar with label and refresh button
        title_bar = QtWidgets.QFrame()
        title_bar.setStyleSheet("background-color: #3A3A3A;")
        title_bar_layout = FramelessHLayout()
        title_bar_layout.setContentsMargins(5, 5, 5, 5)
        title_bar.setLayout(title_bar_layout)

        # Add title label
        title_label = dayu_widgets.MLabel("眼神光样式")
        title_label.setStyleSheet("color: #ffffff; font-weight: bold;")
        title_bar_layout.addWidget(title_label)

        # Add stretch to push refresh button to the right
        title_bar_layout.addStretch()

        # Add refresh button with refresh icon
        self.refresh_button = dayu_widgets.MToolButton().icon_only().svg("refresh.svg")
        self.refresh_button.setFixedSize(24, 24)
        self.refresh_button.setIconSize(QtCore.QSize(16, 16))
        self.refresh_button.setToolTip("刷新眼神光预设")
        self.refresh_button.setStyleSheet(
            """
            MToolButton {
                background-color: transparent;
                border: none;
            }
            MToolButton:hover {
                background-color: #5a5a5a;
                border-radius: 3px;
            }
            MToolButton:pressed {
                background-color: #404040;
            }
        """
        )
        title_bar_layout.addWidget(self.refresh_button)

        layout.addWidget(title_bar)

        # Add item view
        self.eye_light_item_view = CatchlightItemView()
        layout.addWidget(self.eye_light_item_view)

        return container

    def create_list_widget(self):
        """Create the second section with list."""
        # Create container
        container = QtWidgets.QFrame()
        container.setFrameShape(QtWidgets.QFrame.StyledPanel)
        container.setStyleSheet(
            """
            QFrame {
                background-color: #2a2a2a;
                border: 1px solid #444444;
                border-radius: 4px;
            }
        """
        )

        # Create layout
        layout = FramelessVLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        container.setLayout(layout)

        # Create title bar with label and delete button
        title_bar = QtWidgets.QFrame()
        title_bar.setStyleSheet("background-color: #3A3A3A;")
        title_bar_layout = FramelessHLayout()
        title_bar_layout.setContentsMargins(5, 5, 5, 5)
        title_bar.setLayout(title_bar_layout)

        # Add title label
        title_label = dayu_widgets.MLabel("眼神光列表")
        title_label.setStyleSheet("color: #ffffff; font-weight: bold;")
        title_bar_layout.addWidget(title_label)

        # Add stretch to push delete button to the right
        title_bar_layout.addStretch()

        # Add delete button with trash icon
        self.delete_button = dayu_widgets.MToolButton().icon_only().svg("trash.svg")
        self.delete_button.setFixedSize(24, 24)
        self.delete_button.setIconSize(QtCore.QSize(16, 16))
        self.delete_button.setToolTip("删除选中的眼神光")
        self.delete_button.setStyleSheet(
            """
            MToolButton {
                background-color: transparent;
                border: none;
            }
            MToolButton:hover {
                background-color: #5A5A5A;
                border-radius: 3px;
            }
            MToolButton:pressed {
                background-color: #2A2A2A;
            }
        """
        )
        title_bar_layout.addWidget(self.delete_button)

        layout.addWidget(title_bar)

        # Add list widget
        self.eye_light_list = CatchlightListWidget()
        layout.addWidget(self.eye_light_list)

        return container

    def create_empty_widget(self):
        """Create the third section with catchlight details widget."""
        # Create container
        container = QtWidgets.QFrame()
        container.setFrameShape(QtWidgets.QFrame.StyledPanel)
        container.setStyleSheet(
            """
            QFrame {
                background-color: #2a2a2a;
                border: 1px solid #444444;
                border-radius: 4px;
            }
        """
        )

        # Create layout
        layout = FramelessVLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        container.setLayout(layout)

        # Create title bar with label
        title_bar = QtWidgets.QFrame()
        title_bar.setStyleSheet("background-color: #3A3A3A;")
        title_bar_layout = FramelessHLayout()
        title_bar_layout.setContentsMargins(5, 5, 5, 5)
        title_bar.setLayout(title_bar_layout)

        # Add title label
        title_label = dayu_widgets.MLabel("眼神光属性")
        title_label.setStyleSheet("color: #ffffff; font-weight: bold;")
        title_bar_layout.addWidget(title_label)

        # Add stretch to push refresh button to the right
        title_bar_layout.addStretch()

        # Add reset button
        self.reset_details_button = dayu_widgets.MToolButton().icon_only().svg("reset.svg")
        self.reset_details_button.setFixedSize(24, 24)
        self.reset_details_button.setIconSize(QtCore.QSize(16, 16))
        self.reset_details_button.setToolTip("恢复默认参数")
        self.reset_details_button.setStyleSheet(
            """
            MToolButton {
                background-color: transparent;
                border: none;
            }
            MToolButton:hover {
                background-color: #5A5A5A;
                border-radius: 3px;
            }
            MToolButton:pressed {
                background-color: #2A2A2A;
            }
        """
        )
        title_bar_layout.addWidget(self.reset_details_button)

        layout.addWidget(title_bar)

        # Add catchlight details widget
        self.catchlight_details = CatchlightDetailsWidget()
        layout.addWidget(self.catchlight_details)

        return container

    def style_splitter_handles(self):
        """Style the splitter handles to make them more visible and user-friendly"""
        # Apply stylesheet to make handles more visible
        handle_style = """
            QSplitter::handle {
                background-color: #323232;
                border-radius: 2px;
            }
            QSplitter::handle:hover {
                background-color: #5a5a5a;
            }
            QSplitter::handle:pressed {
                background-color: #2683d9;
            }
        """
        self.splitter.setStyleSheet(handle_style)

        # Set cursor to horizontal resize cursor for better UX
        for i in range(self.splitter.count() - 1):  # For each handle
            handle = self.splitter.handle(i + 1)
            handle.setCursor(QtCore.Qt.SplitHCursor)

    def connect_signals(self):
        """Connect signals between components."""
        self.delete_button.clicked.connect(self.slot_delete_selected_items)
        self.refresh_button.clicked.connect(self.slot_refresh_catchlights)
        self.reset_details_button.clicked.connect(self.catchlight_details.slot_reset_details_clicked)

        # Connect list selection change to update details UI
        self.eye_light_list.itemSelectionChanged.connect(self.slot_list_selection_changed)

    @QtCore.Slot()
    def slot_list_selection_changed(self):
        """Handle list selection change and update details UI."""
        selected_items = self.eye_light_list.selectedItems()
        if selected_items:
            # Get the first selected item (single selection mode)
            selected_item = selected_items[0]
            catchlight_name = self.eye_light_list.get_item_name(selected_item)
            if catchlight_name:
                # Update the details UI with current catchlight state
                self.catchlight_details.update_ui_from_catchlight_state(catchlight_name)

    @QtCore.Slot()
    def slot_delete_selected_items(self):
        """Delete selected items from the eye light list."""
        selected_items = self.eye_light_list.selectedItems()
        self.eye_light_list.delete_items(selected_items)

    @QtCore.Slot()
    def slot_refresh_catchlights(self):
        """Refresh catchlight presets from API and update the item view."""
        try:
            # Refresh the catchlight data in the item view
            self.eye_light_item_view.setup_catchlight_data()
        except Exception as e:
            MMessage.error(f"刷新眼神光时出错: {e}", parent=self)

    @QtCore.Slot()
    def slot_reset_catchlight_system(self):
        """Handle catchlight system reset signal from build process.

        This slot is called when the build process completes and needs to reset
        the catchlight UI to reflect the current state of the scene.
        """
        try:
            # Clear the current list
            self.eye_light_list.clear()

            # Refresh the catchlight presets in the item view
            self.eye_light_item_view.setup_catchlight_data()

            # Reinitialize the catchlight list with existing catchlights
            self.init_data()

        except Exception as e:
            MMessage.error(f"重置眼神光系统时出错: {e}", parent=self)
