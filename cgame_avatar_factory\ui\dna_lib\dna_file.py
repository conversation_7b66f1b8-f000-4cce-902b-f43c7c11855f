# Import third-party modules
from qtpy import QtGui

# Import local modules
from cgame_avatar_factory import constants as const
from cgame_avatar_factory import utils


class DnaFile:
    def __init__(
        self,
        dna_id,
        dna_file_path,
        thumbnail_file_path,
        modified_date=None,
        character_name=None,
        gender=None,
        age=None,
        race=None,
        is_official_dna=True,
        thumbnail_size=128,
    ):
        self.dna_id = dna_id
        self.dna_file_path = dna_file_path
        self.thumbnail_file_path = thumbnail_file_path
        self.modified_date = modified_date
        self.character_name = character_name
        self.gender = gender
        self.age = age
        self.race = race
        self.thumbnail_size = thumbnail_size
        self.is_official_dna = is_official_dna

        self._valid = False
        self.validate_dna_file()
        self.generate_thumbnail_icon()

    def validate_dna_file(self):
        if not utils.path_available(self.dna_file_path):
            raise ValueError("DNA file {} is not available.".format(self.dna_file_path))

        self.file_name = utils.get_file_name(self.dna_file_path)

        # Check if the file has one of the supported extensions
        file_extension = utils.get_file_extension(self.dna_file_path)
        if file_extension.lower() not in const.SUPPORTED_DNA_FILE_EXTENSIONS:
            raise ValueError(
                "File {} is not a supported character file. Supported extensions: {}".format(
                    self.dna_file_path,
                    ", ".join(const.SUPPORTED_DNA_FILE_EXTENSIONS),
                ),
            )

        self._valid = True

    def generate_thumbnail_icon(self):
        if self.thumbnail_file_path:
            original_pixmap = QtGui.QPixmap(self.thumbnail_file_path)
            # crop to square
            cropped_pixmap = utils.crop_pixmap_to_square(original_pixmap, self.thumbnail_size)
            self.icon = QtGui.QIcon(cropped_pixmap)

    def to_dict(self):
        return (
            {
                "dna_id": self.dna_id,
                "dna_file_path": self.dna_file_path,
                "thumbnail_file_path": self.thumbnail_file_path,
                "modified_date": self.modified_date,
                "character_name": self.character_name,
                "gender": self.gender,
                "age": self.age,
                "race": self.race,
                "is_official_dna": self.is_official_dna,
            }
            if self._valid
            else None
        )
