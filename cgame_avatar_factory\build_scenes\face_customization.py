# Import built-in modules
import json
import logging
import os

# Import third-party modules
import maya.cmds as cmds

# Import local modules
import cgame_avatar_factory.constants as const

# Set up module-level logger
logger = logging.getLogger(__name__)


def remove_selection_changed_script_jobs():
    all_jobs = cmds.scriptJob(listJobs=True)
    if all_jobs:
        for job in all_jobs:
            job_id = job.split()[0]
            job_info = job.split()[1:]
            if len(job_info) > 0 and "SelectionChanged" in job_info[0]:
                cmds.scriptJob(kill=int(job_id[:-1]), force=True)

    selected = cmds.ls(selection=True)
    if selected:
        for obj in selected:
            suffix = const.SUFFIX[0] if obj.endswith(const.SUFFIX[0]) else const.SUFFIX[1]
            symmetrical_point = obj[:-2] + (const.SUFFIX[1] if suffix == const.SUFFIX[0] else const.SUFFIX[0])
            if cmds.objExists(symmetrical_point):
                delete_controller_connections(symmetrical_point)


def delete_controller_connections(ctrl_name):
    cmds.select(clear=True)
    for attr in const.ATTR_LIST:
        full_attr = f"{ctrl_name}{attr}"
        if cmds.objExists(full_attr):
            source_connections = cmds.listConnections(full_attr, source=True, destination=False, plugs=True) or []
            for source in source_connections:
                cmds.disconnectAttr(source, full_attr)


def delete_all_sockets():
    children = cmds.listRelatives("root", children=True, allDescendents=True) or []
    sockets = [socket for socket in children if "_Socket" in socket]

    for socket in sockets:
        cmds.delete(socket)


def add_socket_to_bone(bone_name, group_name, position):
    if cmds.objExists(bone_name):
        socket = cmds.group(empty=True, name=group_name)

        cmds.parent(socket, bone_name)
        if cmds.objExists(socket):
            cmds.setAttr(f"{socket}{const.ATTR_LIST[0]}", position["x"])
            cmds.setAttr(f"{socket}{const.ATTR_LIST[1]}", position["y"])
            cmds.setAttr(f"{socket}{const.ATTR_LIST[2]}", position["z"])

    else:
        logger.error(f"Bone '{bone_name}' does not exist.")


def add_sockets_from_json(socket_positions, json_file):
    with open(socket_positions, "r", encoding="utf-8") as file:
        socket_positions = json.load(file)
        position_mapping = {item["Name"]: item["Position"] for item in socket_positions}

    with open(json_file, "r", encoding="utf-8") as file:
        data = json.load(file)
        for item in data:
            bone_name = item["bone"]
            socket_name = item["socket_name"]
            socket_position = position_mapping.get(socket_name)
            add_socket_to_bone(bone_name, socket_name, socket_position)


def set_driven_key(driver_ctrl, target_ctrl, axis, end_driver_value, begin_driver_value=0):
    try:
        cmds.setDrivenKeyframe(
            target_ctrl,
            currentDriver=f"{driver_ctrl}.{axis}",
            driverValue=begin_driver_value,
            value=0,
        )
        cmds.setDrivenKeyframe(
            target_ctrl,
            currentDriver=f"{driver_ctrl}.{axis}",
            driverValue=end_driver_value,
            value=1,
        )
    except Exception as e:
        logger.error(f"Error setting driven key for {driver_ctrl}: {str(e)}")


def setup_control_drivers(ctrl_name, sphere, pose_asset, symmetric=True):
    for direction, attr in pose_asset.items():
        target_ctrl = f"{const.MAIN_CTRL_NAME}.{attr}"
        driver_ctrl = sphere

        if not cmds.objExists(target_ctrl):
            logger.warning(f"Target controller {target_ctrl} not found, skipping...")
            continue

        is_right_ctrl = ctrl_name.endswith(const.SUFFIX[1])
        is_left_ctrl = ctrl_name.endswith(const.SUFFIX[0])

        if is_right_ctrl:
            symmetric_ctrl = ctrl_name.replace(const.SUFFIX[1], const.SUFFIX[0])
            if not cmds.objExists(symmetric_ctrl):
                logger.warning(f"Symmetric controller {symmetric_ctrl} not found")
                symmetric_ctrl = None
        elif is_left_ctrl:
            symmetric_ctrl = ctrl_name.replace(const.SUFFIX[0], const.SUFFIX[1])
            if not cmds.objExists(symmetric_ctrl):
                logger.warning(f"Symmetric controller {symmetric_ctrl} not found")
                symmetric_ctrl = None
        else:
            symmetric_ctrl = None

        if cmds.upAxis(q=True, axis=True).lower() == "y":
            DIRECTION_CONFIG = {
                "translate": {
                    const.Direction.FORWARD.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.TRANSLATE_Z.value,
                        const.DirectionKey.VALUE.value: 1,
                    },
                    const.Direction.BACKWARD.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.TRANSLATE_Z.value,
                        const.DirectionKey.VALUE.value: -1,
                    },
                    const.Direction.IN.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.TRANSLATE_X.value,
                        const.DirectionKey.VALUE.value: lambda: 1 if is_right_ctrl else -1,
                    },
                    const.Direction.OUT.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.TRANSLATE_X.value,
                        const.DirectionKey.VALUE.value: lambda: -1 if is_right_ctrl else 1,
                    },
                    const.Direction.UP.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.TRANSLATE_Y.value,
                        const.DirectionKey.VALUE.value: 1,
                    },
                    const.Direction.DOWN.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.TRANSLATE_Y.value,
                        const.DirectionKey.VALUE.value: -1,
                    },
                },
                "rotate": {
                    const.Direction.ROTATE_V.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.ROTATE_Z.value,
                        const.DirectionKey.SIGN.value: {
                            const.DirectionKey.LEFT.value: 1,
                            const.DirectionKey.RIGHT.value: -1,
                        },
                    },
                    const.Direction.ROTATE_A.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.ROTATE_Z.value,
                        const.DirectionKey.SIGN.value: {
                            const.DirectionKey.LEFT.value: -1,
                            const.DirectionKey.RIGHT.value: 1,
                        },
                    },
                },
                "scale": {
                    const.Direction.ENLARGE_H.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.SCALE_X.value,
                        const.DirectionKey.TARGET.value: 1.5,
                        const.DirectionKey.BEGIN.value: 1,
                    },
                    const.Direction.REDUCE_H.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.SCALE_X.value,
                        const.DirectionKey.TARGET.value: 0.5,
                        const.DirectionKey.BEGIN.value: 1,
                    },
                    const.Direction.ENLARGE_V.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.SCALE_Y.value,
                        const.DirectionKey.TARGET.value: 1.5,
                        const.DirectionKey.BEGIN.value: 1,
                    },
                    const.Direction.REDUCE_V.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.SCALE_Y.value,
                        const.DirectionKey.TARGET.value: 0.5,
                        const.DirectionKey.BEGIN.value: 1,
                    },
                },
            }
        else:
            DIRECTION_CONFIG = {
                "translate": {
                    const.Direction.FORWARD.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.TRANSLATE_Y.value,
                        const.DirectionKey.VALUE.value: -1,
                    },
                    const.Direction.BACKWARD.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.TRANSLATE_Y.value,
                        const.DirectionKey.VALUE.value: 1,
                    },
                    const.Direction.IN.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.TRANSLATE_X.value,
                        const.DirectionKey.VALUE.value: lambda: 1 if is_right_ctrl else -1,
                    },
                    const.Direction.OUT.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.TRANSLATE_X.value,
                        const.DirectionKey.VALUE.value: lambda: -1 if is_right_ctrl else 1,
                    },
                    const.Direction.UP.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.TRANSLATE_Z.value,
                        const.DirectionKey.VALUE.value: 1,
                    },
                    const.Direction.DOWN.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.TRANSLATE_Z.value,
                        const.DirectionKey.VALUE.value: -1,
                    },
                },
                "rotate": {
                    const.Direction.ROTATE_V.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.ROTATE_Y.value,
                        const.DirectionKey.SIGN.value: {
                            const.DirectionKey.LEFT.value: -1,
                            const.DirectionKey.RIGHT.value: 1,
                        },
                    },
                    const.Direction.ROTATE_A.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.ROTATE_Y.value,
                        const.DirectionKey.SIGN.value: {
                            const.DirectionKey.LEFT.value: 1,
                            const.DirectionKey.RIGHT.value: -1,
                        },
                    },
                },
                "scale": {
                    const.Direction.ENLARGE_H.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.SCALE_X.value,
                        const.DirectionKey.TARGET.value: 1.5,
                        const.DirectionKey.BEGIN.value: 1,
                    },
                    const.Direction.REDUCE_H.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.SCALE_X.value,
                        const.DirectionKey.TARGET.value: 0.5,
                        const.DirectionKey.BEGIN.value: 1,
                    },
                    const.Direction.ENLARGE_V.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.SCALE_Z.value,
                        const.DirectionKey.TARGET.value: 1.5,
                        const.DirectionKey.BEGIN.value: 1,
                    },
                    const.Direction.REDUCE_V.value: {
                        const.DirectionKey.AXIS.value: const.AttributeName.SCALE_Z.value,
                        const.DirectionKey.TARGET.value: 0.5,
                        const.DirectionKey.BEGIN.value: 1,
                    },
                },
            }

        if direction in [
            const.Direction.FORWARD.value,
            const.Direction.BACKWARD.value,
            const.Direction.IN.value,
            const.Direction.OUT.value,
            const.Direction.UP.value,
            const.Direction.DOWN.value,
        ]:
            config = DIRECTION_CONFIG["translate"][direction]
            axis = config[const.DirectionKey.AXIS.value]

            driver_value = (
                config[const.DirectionKey.VALUE.value]()
                if callable(config[const.DirectionKey.VALUE.value])
                else config[const.DirectionKey.VALUE.value]
            )

            set_driven_key(driver_ctrl, target_ctrl, axis, driver_value)

        elif direction in [const.Direction.ROTATE_V.value, const.Direction.ROTATE_A.value]:
            config = DIRECTION_CONFIG["rotate"][direction]
            axis = config[const.DirectionKey.AXIS.value]

            side = const.DirectionKey.LEFT.value if is_left_ctrl else const.DirectionKey.RIGHT.value
            driver_value = 45 * config[const.DirectionKey.SIGN.value][side]

            set_driven_key(driver_ctrl, target_ctrl, axis, driver_value)

        elif direction in [
            const.Direction.ENLARGE_H.value,
            const.Direction.REDUCE_H.value,
            const.Direction.ENLARGE_V.value,
            const.Direction.REDUCE_V.value,
        ]:
            config = DIRECTION_CONFIG["scale"][direction]

            set_driven_key(
                driver_ctrl,
                target_ctrl,
                config[const.DirectionKey.AXIS.value],
                config[const.DirectionKey.TARGET.value],
                config[const.DirectionKey.BEGIN.value],
            )


def create_control_point(ctrl_data, mode, symmetric=True):
    bone_name = ctrl_data["bone"]
    ctrl_name = ctrl_data["Name"]
    pose_asset = ctrl_data["pose_asset"]

    if not cmds.objExists("ctrl_red_material"):
        red_material = cmds.shadingNode(
            "lambert",
            asShader=True,
            name="ctrl_red_material",
        )
        cmds.setAttr(red_material + ".color", 1, 0, 0, type="double3")
        cmds.setAttr(red_material + ".ambientColor", 1, 0, 0, type="double3")
        cmds.setAttr(red_material + ".diffuse", 0)
    else:
        red_material = "ctrl_red_material"

    if not cmds.objExists(bone_name):
        logger.warning(f"Warning: Bone {bone_name} not found, skipping...")
        return

    if mode == "简单":
        sphere = cmds.polySphere(
            name=ctrl_name,
            radius=0.2,
            subdivisionsX=8,
            subdivisionsY=8,
        )[0]
    elif mode == "精细":
        sphere = cmds.polySphere(
            name=ctrl_name,
            radius=0.13,
            subdivisionsX=8,
            subdivisionsY=8,
        )[0]

    min_translate = -1
    max_translate = 1
    min_rotation = -45
    max_rotation = 45
    min_scale = 0.5
    max_scale = 1.5
    cmds.transformLimits(
        sphere,
        tx=(min_translate, max_translate),
        etx=(True, True),
        ty=(min_translate, max_translate),
        ety=(True, True),
        tz=(min_translate, max_translate),
        etz=(True, True),
        rx=(min_rotation, max_rotation),
        erx=(True, True),
        ry=(min_rotation, max_rotation),
        ery=(True, True),
        rz=(min_rotation, max_rotation),
        erz=(True, True),
        sx=(min_scale, max_scale),
        esx=(True, True),
        sy=(min_scale, max_scale),
        esy=(True, True),
        sz=(min_scale, max_scale),
        esz=(True, True),
    )

    cmds.select(sphere)
    cmds.hyperShade(assign=red_material)

    # Turn off shadows for the controller shape
    shape = cmds.listRelatives(sphere, shapes=True)[0]
    cmds.setAttr(f"{shape}.castsShadows", 0)

    ctrl_group = cmds.group(sphere, name=f"{ctrl_name}{const.GRP_ROOT}")
    bone_pos = cmds.xform(bone_name, query=True, worldSpace=True, translation=True)
    cmds.xform(ctrl_group, worldSpace=True, translation=bone_pos)

    setup_control_drivers(ctrl_name, sphere, pose_asset, symmetric)


def create_and_setup_control_point(ctrl_data, main_group, mode, symmetric=True):
    create_control_point(ctrl_data, mode, symmetric)
    cmds.parent(f"{ctrl_data['Name']}{const.GRP_ROOT}", main_group)


def load_and_create_controls(ctrl_points_file, group_name):
    if "简单" in ctrl_points_file:
        mode = "简单"
    else:
        mode = "精细"
    with open(ctrl_points_file, "r", encoding="utf-8") as f:
        ctrl_points = json.load(f)
    if not cmds.objExists(group_name):
        main_group = cmds.group(empty=True, name=group_name)
    else:
        main_group = group_name
    for ctrl_data in ctrl_points:
        if ctrl_data["Name"] not in const.EXCEPT_CTRL:
            create_and_setup_control_point(ctrl_data, main_group, mode)


def delete_symmetry_expressions():
    """Delete all symmetry expressions"""
    expressions = cmds.ls(type="expression") or []
    mirror_expressions = [expr for expr in expressions if "mirror_expression" in expr]

    if mirror_expressions:
        logger.debug(f"Deleting {len(mirror_expressions)} mirror expressions")
        for expr in mirror_expressions:
            try:
                cmds.delete(expr)
            except Exception as e:
                logger.warning(f"Failed to delete expression {expr}: {str(e)}")


def on_selection_change():
    global _processing_selection

    if _processing_selection:
        logger.debug("Already processing selection event, skipping duplicate call")
        return

    try:
        _processing_selection = True
        logger.debug("Starting selection event processing")

        selected = cmds.ls(selection=True)
        if selected:
            # Only process the first selected controller to avoid creating multiple expressions
            for obj in selected:
                if "CTRL" in obj and not obj.endswith(const.GRP_ROOT):
                    logger.debug(f"Processing controller: {obj}")
                    get_symmetrical_point(control_point=obj)
                    break  # Only process the first controller
        else:
            delete_symmetry_expressions()
    finally:
        # Ensure the flag is reset after processing, regardless of whether an exception occurred
        _processing_selection = False
        logger.debug("Completed selection event processing")


def symmetry_mode():
    # Remove all existing selection change script jobs to avoid duplicate registration
    remove_selection_changed_script_jobs()
    # Register new selection change event
    job_id = cmds.scriptJob(event=["SelectionChanged", on_selection_change])
    logger.info(f"Registered selection change event, job ID: {job_id}")


_processing_selection = False
last_control_point = None


def clean_expressions_for_control(control_point):
    """Clean up all expressions related to the specified controller"""
    if not control_point:
        return

    # Find all expression nodes
    all_expressions = cmds.ls(type="expression") or []

    # Filter out expressions related to the current controller
    related_expressions = [expr for expr in all_expressions if control_point in expr]

    # Delete these expressions
    if related_expressions:
        logger.debug(f"Cleaning up {len(related_expressions)} expressions for controller {control_point}")
        for expr in related_expressions:
            if cmds.objExists(expr):
                try:
                    cmds.delete(expr)
                except Exception as e:
                    logger.warning(f"Failed to delete expression {expr}: {str(e)}")


def get_symmetrical_point(control_point):
    global last_control_point

    # Clean up all expressions related to the current controller
    clean_expressions_for_control(control_point)

    # If there is a previous controller, clean up its expressions
    if last_control_point and last_control_point != control_point:
        clean_expressions_for_control(last_control_point)

    if Iosymmetrical().return_symmetrical():
        last_control_point = control_point

        suffix = const.SUFFIX[0] if control_point.endswith(const.SUFFIX[0]) else const.SUFFIX[1]
        symmetrical_point = control_point[:-2] + (const.SUFFIX[1] if suffix == const.SUFFIX[0] else const.SUFFIX[0])

        if not cmds.objExists(symmetrical_point):
            logger.warning(f"Symmetrical point {symmetrical_point} does not exist")
            return None

        create_symmetrical_expression(control_point, symmetrical_point)
        return symmetrical_point


def create_symmetrical_expression(control_point, symmetrical_point):
    attrs = [
        (const.ATTR_NAME_LIST[0], "-"),
        (const.ATTR_NAME_LIST[1], ""),
        (const.ATTR_NAME_LIST[2], ""),
        (const.ATTR_NAME_LIST[3], ""),
        (const.ATTR_NAME_LIST[4], "-"),
        (const.ATTR_NAME_LIST[5], "-"),
        (const.ATTR_NAME_LIST[6], ""),
        (const.ATTR_NAME_LIST[7], ""),
        (const.ATTR_NAME_LIST[8], ""),
    ]

    for attr, _ in attrs:
        target_attr = f"{symmetrical_point}.{attr}"

        input_connection = cmds.connectionInfo(target_attr, sourceFromDestination=True)
        if input_connection:
            cmds.disconnectAttr(input_connection, target_attr)

        if cmds.keyframe(target_attr, query=True):
            cmds.cutKey(target_attr)
    expr = (
        f"{symmetrical_point}{const.ATTR_LIST[0]} = -{control_point}{const.ATTR_LIST[0]};\n"
        f"{symmetrical_point}{const.ATTR_LIST[1]} = {control_point}{const.ATTR_LIST[1]};\n"
        f"{symmetrical_point}{const.ATTR_LIST[2]} = {control_point}{const.ATTR_LIST[2]};\n"
        f"{symmetrical_point}{const.ATTR_LIST[3]} = {control_point}{const.ATTR_LIST[3]};\n"
        f"{symmetrical_point}{const.ATTR_LIST[4]} = -{control_point}{const.ATTR_LIST[4]};\n"
        f"{symmetrical_point}{const.ATTR_LIST[5]} = -{control_point}{const.ATTR_LIST[5]};\n"
        f"{symmetrical_point}{const.ATTR_LIST[6]} = {control_point}{const.ATTR_LIST[6]};\n"
        f"{symmetrical_point}{const.ATTR_LIST[7]} = {control_point}{const.ATTR_LIST[7]};\n"
        f"{symmetrical_point}{const.ATTR_LIST[8]} = {control_point}{const.ATTR_LIST[8]};"
    )
    expr_name = f"{control_point}_mirror_expression"
    cmds.expression(name=expr_name, string=expr)


class Iosymmetrical:
    def __init__(self):
        self.is_symmetrical = True

    def return_symmetrical(self):
        return self.is_symmetrical

    def set_symmetrical_true(self):
        self.is_symmetrical = True

    def set_symmetrical_false(self):
        self.is_symmetrical = False


def clear_all_ctrls():
    ctrl_groups = [const.FACE_CTRL_BASIC_GRP, const.FACE_CTRL_DETAILED_GRP]
    for ctrl_group in ctrl_groups:
        if cmds.objExists(ctrl_group):
            all_ctrls = cmds.listRelatives(ctrl_group, allDescendents=True, type="transform") or []
            for ctrl in all_ctrls:
                if not ctrl.endswith(const.GRP_ROOT):
                    expressions = cmds.listConnections(ctrl, type="expression")
                    if expressions:
                        cmds.delete(expressions)
                    for attr in const.ATTR_LIST[:6]:
                        cmds.setAttr(f"{ctrl}{attr}", 0)
                    for attr in const.ATTR_LIST[6:]:
                        cmds.setAttr(f"{ctrl}{attr}", 1)
    update_all_controllers_in_group(
        const.FACE_CTRL_DETAILED_GRP,
        os.path.join(const.FACECUSTOMIZATION_PATH, const.FACE_CONTROL_JSON_FILES[0]),
    )
    update_all_controllers_in_group(
        const.FACE_CTRL_BASIC_GRP,
        os.path.join(const.FACECUSTOMIZATION_PATH, const.FACE_CONTROL_JSON_FILES[1]),
    )


def add_eye_control_attr():
    for ctrl_attr in const.EXCEPT_CTRL_ATTR:
        if const.SUFFIX[0] in ctrl_attr:
            side = "L"
        else:
            side = "R"

        eye_ctrl_point = f"B_EYE_CTRL_{side}"
        cmds.addAttr(
            eye_ctrl_point,
            longName=ctrl_attr,
            attributeType="float",
            defaultValue=0.0,
            minValue=0,
            maxValue=1,
        )
        cmds.setAttr(f"{eye_ctrl_point}.{ctrl_attr}", keyable=True)
        target_ctrl = f"{const.MAIN_CTRL_NAME}.{ctrl_attr}"
        driver_ctrl = f"{eye_ctrl_point}.{ctrl_attr}"
        cmds.setDrivenKeyframe(
            target_ctrl,
            currentDriver=f"{driver_ctrl}",
            driverValue=0,
            value=0,
        )
        cmds.setDrivenKeyframe(
            target_ctrl,
            currentDriver=f"{driver_ctrl}",
            driverValue=1,
            value=1,
        )


def create_controller():
    """Create controller for face customization."""
    socket_positions = os.path.join(const.FACECUSTOMIZATION_PATH, const.FACE_CONTROL_JSON_FILES[2])
    add_sockets_from_json(
        socket_positions,
        os.path.join(const.FACECUSTOMIZATION_PATH, const.FACE_CONTROL_JSON_FILES[1]),
    )
    add_sockets_from_json(
        socket_positions,
        os.path.join(const.FACECUSTOMIZATION_PATH, const.FACE_CONTROL_JSON_FILES[0]),
    )

    load_and_create_controls(
        os.path.join(const.FACECUSTOMIZATION_PATH, const.FACE_CONTROL_JSON_FILES[1]),
        const.FACE_CTRL_BASIC_GRP,
    )
    load_and_create_controls(
        os.path.join(const.FACECUSTOMIZATION_PATH, const.FACE_CONTROL_JSON_FILES[0]),
        const.FACE_CTRL_DETAILED_GRP,
    )
    update_all_controllers_in_group(
        const.FACE_CTRL_BASIC_GRP,
        os.path.join(const.FACECUSTOMIZATION_PATH, const.FACE_CONTROL_JSON_FILES[1]),
    )
    update_all_controllers_in_group(
        const.FACE_CTRL_DETAILED_GRP,
        os.path.join(const.FACECUSTOMIZATION_PATH, const.FACE_CONTROL_JSON_FILES[0]),
    )
    add_eye_control_attr()

    change_to_basic()
    symmetry_mode()
    logger.info("create_controller done")


def toggle_symmetry():
    is_symmetrical = Iosymmetrical().return_symmetrical()
    on_selection_change()
    if is_symmetrical:
        logger.info("Symmetry has been enabled")
    else:
        logger.info("Symmetry has been disabled")


def remove_controller():
    if cmds.objExists(const.FACE_CTRL_BASIC_GRP):
        cmds.delete(const.FACE_CTRL_BASIC_GRP)
    if cmds.objExists(const.FACE_CTRL_DETAILED_GRP):
        cmds.delete(const.FACE_CTRL_DETAILED_GRP)
    delete_all_sockets()
    remove_selection_changed_script_jobs()


def update_controller_to_socket(controller_name, socket_name):
    if cmds.objExists(controller_name) and cmds.objExists(socket_name):
        socket_position = cmds.xform(
            socket_name,
            query=True,
            worldSpace=True,
            translation=True,
        )
        cmds.xform(controller_name, worldSpace=True, translation=socket_position)

    else:
        logger.error(
            f"Controller '{controller_name}' or socket '{socket_name}' does not exist.",
        )


def update_all_controllers_in_group(group_name, json_file):
    if cmds.objExists(group_name):
        controllers = cmds.listRelatives(group_name, children=True, fullPath=True)
        controllers = [ctrl for ctrl in controllers if ctrl.endswith(const.GRP_ROOT)]
        if controllers:
            with open(json_file, "r", encoding="utf-8") as file:
                data = json.load(file)
                socket_mapping = {item["Name"]: (item["socket_name"], item["bone"]) for item in data}

            for controller in controllers:
                if "|FACE_CTRL_DETAILED_GRP|" in controller:
                    ctrl_name = controller.replace("|FACE_CTRL_DETAILED_GRP|", "")
                    ctrl_name = ctrl_name.replace(const.GRP_ROOT, "")
                else:
                    ctrl_name = controller.replace("|FACE_CTRL_BASIC_GRP|", "")
                    ctrl_name = ctrl_name.replace(const.GRP_ROOT, "")
                socket_name = socket_mapping.get(ctrl_name)[0]
                if socket_name:
                    update_controller_to_socket(ctrl_name + const.GRP_ROOT, socket_name)
                else:
                    logger.error(f"No socket found for controller '{ctrl_name}'.")
        else:
            logger.error(f"No controllers found in group '{group_name}'.")
    else:
        logger.error(f"Group '{group_name}' does not exist.")


def change_to_detailed():
    update_all_controllers_in_group(
        const.FACE_CTRL_DETAILED_GRP,
        os.path.join(const.FACECUSTOMIZATION_PATH, const.FACE_CONTROL_JSON_FILES[0]),
    )

    if cmds.objExists(const.FACE_CTRL_BASIC_GRP):
        cmds.hide(const.FACE_CTRL_BASIC_GRP)

    if cmds.objExists(const.FACE_CTRL_DETAILED_GRP):
        cmds.showHidden(const.FACE_CTRL_DETAILED_GRP)
    symmetry_mode()


def change_to_basic():
    if cmds.objExists(const.FACE_CTRL_DETAILED_GRP):
        cmds.hide(const.FACE_CTRL_DETAILED_GRP)

    if cmds.objExists(const.FACE_CTRL_BASIC_GRP):
        cmds.showHidden(const.FACE_CTRL_BASIC_GRP)
    symmetry_mode()
