# Import third-party modules
from qtpy import QtCore
from qtpy import QtWidgets


class WeightLabel(QtWidgets.QWidget):
    sig_text_changed = QtCore.Signal(str)
    sig_weight_changed = QtCore.Signal(float)

    def __init__(self, parent=None):
        super(WeightLabel, self).__init__(parent)
        self._weight = 0.0
        self._init_ui()

    def _init_ui(self):
        layout = QtWidgets.QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)

        self._label = QtWidgets.QLabel(self)
        self._label.setFixedWidth(60)
        self._label.setAlignment(QtCore.Qt.AlignCenter)
        self._label.setStyleSheet("color: white; background-color: transparent;")
        self._label.setAttribute(QtCore.Qt.WA_TransparentForMouseEvents, False)

        self._edit = QtWidgets.QLineEdit(self)
        self._edit.setFixedWidth(60)
        self._edit.setAlignment(QtCore.Qt.AlignCenter)
        self._edit.setStyleSheet("color: white;")
        self._edit.hide()

        layout.addWidget(self._label)
        layout.addWidget(self._edit)

        self._edit.editingFinished.connect(self._on_editing_finished)

    def set_text(self, text):
        try:
            value = float(text)
            self._weight = value
            formatted_text = f"{value:.2f}"
            self._label.setText(formatted_text)
            self._edit.setText(formatted_text)
            self.sig_weight_changed.emit(value)
        except (ValueError, TypeError):
            formatted_text = text
            self._label.setText(formatted_text)
            self._edit.setText(formatted_text)

    def update_weight(self, weight):
        percentage = int(weight * 100)
        self.set_text(f"{percentage}%")

    def get_text(self):
        return self._label.text()

    def get_weight(self):
        return self._weight

    def set_weight(self, weight):
        self.set_text(str(weight))

    def _on_label_clicked(self, event):
        pass

    def _on_editing_finished(self):
        self._edit.hide()
        self._label.show()
        text = self._edit.text()
        self.set_text(text)
        self.sig_text_changed.emit(text)
