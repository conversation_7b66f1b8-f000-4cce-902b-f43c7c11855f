"""Hair Studio Tab Module.

This module provides the main tab widget for the Hair Studio tool, which serves as the container
for all hair-related functionality including card, XGen, and curve hair tools.
"""

# Import standard library
import os
import logging

# Import Qt modules
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.manager import HairManager

# BaseHairTab will be imported in the setup_ui method to avoid circular imports


class HairStudioTab(QtWidgets.QTabWidget):
    """Main tab widget for the Hair Studio tool.
    
    This class serves as the container for all hair-related tabs including Card, XGen, and Curve tabs.
    It manages the tab switching and coordinates between different hair tools.
    """
    
    def __init__(self, parent=None):
        """Initialize the HairStudioTab.
        
        Args:
            parent (QWidget, optional): The parent widget. Defaults to None.
        """
        super(HairStudioTab, self).__init__(parent)
        self.setObjectName('HairStudioTab')
        
        # Initialize logger
        self._logger = logging.getLogger(__name__)
        
        # Initialize the hair manager
        self._hair_manager = HairManager()
        
        # Initialize UI components
        self.setup_ui()
        
        # Connect signals
        self._connect_signals()
    
    def setup_ui(self):
        """Set up the user interface components.
        
        This method initializes and adds all the tab widgets to the main tab container.
        """
        # Import here to avoid circular imports
        from cgame_avatar_factory.hair_studio.ui.base_hair_tab import BaseHairTab
        
        try:
            # Create tabs with the hair manager
            self.card_tab = BaseHairTab('card', self._hair_manager, self)
            self.xgen_tab = BaseHairTab('xgen', self._hair_manager, self)
            self.curve_tab = BaseHairTab('curve', self._hair_manager, self)
            
            # Add tabs to the tab widget
            self.addTab(self.card_tab, '插片')
            self.addTab(self.xgen_tab, 'XGen')
            self.addTab(self.curve_tab, '曲线')
            
            # Set default tab
            self.setCurrentIndex(0)
            
            # Connect signals
            self.currentChanged.connect(self._on_tab_changed)
            
        except Exception as e:
            self._logger.error("Failed to set up UI: %s", str(e), exc_info=True)
            QtWidgets.QMessageBox.critical(self, "Error", "Failed to initialize Hair Studio UI: {}".format(str(e)))
    
    def _connect_signals(self):
        """Connect signals between components."""
        # Connect component updates
        self._hair_manager.components_updated.connect(self._on_components_updated)
        self._hair_manager.component_selected.connect(self._on_component_selected)
    
    def _on_tab_changed(self, index):
        """Handle tab change events.
        
        Args:
            index (int): Index of the newly selected tab
        """
        try:
            if index < 0:  # No tab selected
                return
                
            # Get the current tab
            current_tab = self.widget(index)
            
            # Refresh the asset library for the current tab
            current_tab.refresh_asset_library()
            
            # Update the component list for the current tab type
            self._update_component_list()
            
        except Exception as e:
            self._logger.error("Error changing tabs: %s", str(e), exc_info=True)
    
    def _on_components_updated(self, components):
        """Handle updates to the component list.
        
        Args:
            components (list): List of HairComponent objects
        """
        try:
            # Update component list in the current tab
            current_tab = self.get_current_tab()
            if current_tab:
                current_tab.update_component_list(components)
        except Exception as e:
            self._logger.error("Error updating component list: %s", str(e), exc_info=True)
    
    def _on_component_selected(self, component):
        """Handle component selection changes.
        
        Args:
            component (HairComponent or None): The selected component, or None if deselected
        """
        try:
            # Update editor area in the current tab
            current_tab = self.get_current_tab()
            if current_tab:
                current_tab.set_selected_component(component)
        except Exception as e:
            self._logger.error("Error handling component selection: %s", str(e), exc_info=True)
    
    def _update_component_list(self):
        """Update the component list for the current tab type."""
        try:
            current_tab = self.get_current_tab()
            if not current_tab:
                return
                
            # Get components filtered by the current tab type
            components = self._hair_manager.get_components()
            filtered_components = [
                comp for comp in components 
                if comp.type == current_tab.hair_type
            ]
            
            # Update the component list
            current_tab.update_component_list(filtered_components)
            
        except Exception as e:
            self._logger.error("Error updating component list: %s", str(e), exc_info=True)
    
    def get_current_tab(self):
        """Get the currently active tab.
        
        Returns:
            BaseHairTab: The currently active tab widget, or None if no tab is selected
        """
        if self.currentIndex() < 0:
            return None
        return self.currentWidget()
    
    def get_hair_manager(self):
        """Get the hair manager instance.
        
        Returns:
            HairManager: The hair manager instance
        """
        return self._hair_manager