# Import built-in modules
from functools import lru_cache
import json
import logging
from typing import Dict
from typing import Optional

# Import third-party modules
from blinker import Namespace
from pydantic import BaseModel
from pydantic import Field
from pydantic import field_validator

# Import local modules
from cgame_avatar_factory import constants as const
from cgame_avatar_factory import utils

merge_data_signals = Namespace()
context_updated = merge_data_signals.signal("context_updated")


class MergeDataContext(BaseModel):
    area_weights: Optional[Dict[str, list]] = Field(
        {},
        description="merge weights for each area",
        validate_default=True,
    )
    dna_infos: Optional[list] = Field(
        [],
        description="infos of merge dna",
        validate_default=True,
    )

    @field_validator("area_weights")
    def _area_weights(cls, value):
        for area_name, area_weights in value.items():
            # valid_area_names = const.MOCK_AREAS_NAME.copy()
            valid_area_names = const.MOCK_AREAS_NAME.copy()

            valid_area_names.extend(const.MOCK_MIRROR_AREAS_NAME)
            if area_name not in valid_area_names:
                raise ValueError(f"invalid area_name: {area_name}")
            if not isinstance(area_weights, list):
                raise ValueError(f"invalid area_weights: {area_weights}")
        return value

    @field_validator("dna_infos")
    def _dna_infos(cls, value):
        for dna_info in value:
            if dna_info.keys() != {"dna_file_path", "thumbnail_file_path", "dna_order"}:
                raise ValueError(f"invalid dna_info keys: {dna_info.keys()}")

            dna_file_path = dna_info["dna_file_path"]
            if not utils.path_available(dna_file_path):
                raise ValueError(f"invalid dna_file_path: {dna_file_path}")

            dna_thumbnail_path = dna_info["thumbnail_file_path"]
            if not utils.path_available(dna_thumbnail_path):
                raise ValueError(f"invalid dna_thumbnail_path: {dna_thumbnail_path}")

            dna_order = dna_info["dna_order"]
            if not isinstance(dna_order, int):
                raise ValueError(f"invalid dna_order: {dna_order}")
        return value

    @property
    def dna_file_paths(self):
        return [dna_info["dna_file_path"] for dna_info in self.dna_infos]

    def update_area_weights(self, area_weights):
        new_data = self.dict()
        new_data["area_weights"].update(area_weights)
        updated_context = self.model_validate(new_data)
        self.area_weights = updated_context.area_weights
        context_updated.send(self)

    def update_dna_infos(self, dna_infos):
        new_data = self.dict()
        new_data["dna_infos"] = dna_infos
        updated_context = self.model_validate(new_data)
        self.dna_infos = updated_context.dna_infos
        context_updated.send(self)

    def save_to_json(self, file_path: str):
        with open(file_path, "w") as file:
            data = self.model_dump()
            logging.getLogger(__name__).info(f"save merge data to {file_path}, data: {data}")
            json.dump(data, file)

    def load_from_json(self, file_path: str):
        with open(file_path, "r") as f:
            data = json.dumps(json.load(f))
            logging.getLogger(__name__).info(f"load merge data from {file_path}, data: {data}")
            data_context = self.model_validate_json(data)
            self.update_dna_infos(data_context.dna_infos)
            self.update_area_weights(data_context.area_weights)


@lru_cache(maxsize=None)
def get_merge_data_context() -> MergeDataContext:
    return MergeDataContext()
