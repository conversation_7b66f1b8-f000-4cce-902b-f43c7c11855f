"""Editor Area Module.

This module provides the editor area widget for the Hair Studio tool.
It allows users to view and edit the properties of the selected hair component.
"""

# Import standard library
import os

# Import Qt modules
from qtpy import QtWidgets, QtCore

# Import dayu widgets
from dayu_widgets import (
    MLabel,
    MLineEdit,
    MSpinBox,
    MDoubleSpinBox,
    MComboBox,
    MCheckBox,
)

# Import local modules
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager


class EditorArea(QtWidgets.QScrollArea):
    """Editor Area Widget.

    This widget displays and allows editing of the properties of the selected hair component.
    It shows different properties based on the type of hair component selected.
    """

    def __init__(self, hair_type, hair_manager=None, parent=None):
        """Initialize the EditorArea.

        Args:
            hair_type (str): Type of hair ('card', 'xgen', 'curve')
            hair_manager (HairManager, optional): The hair manager instance. If None, a new one will be created.
            parent (QWidget, optional): The parent widget. Defaults to None.
        """
        super(<PERSON><PERSON><PERSON>, self).__init__(parent)
        self.hair_type = hair_type
        self.object_name = "{}EditorArea".format(hair_type.capitalize())
        self.setObjectName(self.object_name)

        # Initialize manager
        self.manager = hair_manager if hair_manager is not None else HairManager()

        # Current component data
        self.current_component = None

        # Initialize UI
        self.setup_ui()

    def setup_ui(self):
        """Set up the user interface components."""
        # Create container widget
        container = QtWidgets.QWidget()
        self.setWidget(container)
        self.setWidgetResizable(True)

        # Main layout
        main_layout = QtWidgets.QVBoxLayout(container)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # Title
        self.title_label = MLabel("毛发编辑区")
        self.title_label.setProperty("h2", True)
        main_layout.addWidget(self.title_label)

        # Add separator
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        main_layout.addWidget(separator)

        # Form layout for properties
        self.form_layout = QtWidgets.QFormLayout()
        self.form_layout.setLabelAlignment(
            QtCore.Qt.AlignRight | QtCore.Qt.AlignVCenter
        )
        self.form_layout.setFormAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignTop)
        self.form_layout.setContentsMargins(0, 0, 0, 0)
        self.form_layout.setSpacing(5)

        # Add form layout to scroll area
        main_layout.addLayout(self.form_layout)

        # Add stretch to push content to the top
        main_layout.addStretch()

        # Set initial state
        self.set_component(None)

    def set_component(self, component_data):
        """Set the current component to edit.

        Args:
            component_data (dict): Dictionary containing component data
        """
        self.current_component = component_data

        # Clear existing widgets
        self._clear_form()

        if not component_data:
            self.title_label.setText("未选择组件")
            return

        # Update title
        self.title_label.setText(component_data.get("name", "Unnamed Component"))

        # Add common properties
        self._add_common_properties(component_data)

        # Add type-specific properties
        if self.hair_type == "card":
            self._add_card_properties(component_data)
        elif self.hair_type == "xgen":
            self._add_xgen_properties(component_data)
        elif self.hair_type == "curve":
            self._add_curve_properties(component_data)

    def _clear_form(self):
        """Clear all widgets from the form layout."""
        while self.form_layout.count():
            item = self.form_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
            elif item.layout():
                self._clear_layout(item.layout())

    def _clear_layout(self, layout):
        """Recursively clear a layout and its widgets."""
        while layout.count():
            item = layout.takeAt(0)
            widget = item.widget()
            if widget is not None:
                widget.deleteLater()
            else:
                self._clear_layout(item.layout())

    def _add_common_properties(self, component_data):
        """Add common properties to the form."""
        # Name field
        name_edit = MLineEdit()
        name_edit.setText(component_data.get("name", ""))
        name_edit.textChanged.connect(
            lambda name: self._on_property_changed("name", name)
        )
        self.form_layout.addRow("Name:", name_edit)

        # Visible checkbox
        visible_check = MCheckBox()
        visible_check.setChecked(component_data.get("visible", True))
        visible_check.stateChanged.connect(
            lambda state: self._on_property_changed("visible", state > 0)
        )
        self.form_layout.addRow("Visible:", visible_check)

        # Add separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        self.form_layout.addRow(separator)

    def _add_card_properties(self, component_data):
        """Add card-specific properties to the form."""
        # Width
        width_spin = MDoubleSpinBox()
        width_spin.setRange(0.01, 100.0)
        width_spin.setValue(component_data.get("width", 1.0))
        width_spin.valueChanged.connect(
            lambda value: self._on_property_changed("width", value)
        )
        self.form_layout.addRow("Width:", width_spin)

        # Height
        height_spin = MDoubleSpinBox()
        height_spin.setRange(0.01, 100.0)
        height_spin.setValue(component_data.get("height", 1.0))
        height_spin.valueChanged.connect(
            lambda value: self._on_property_changed("height", value)
        )
        self.form_layout.addRow("Height:", height_spin)

    def _add_xgen_properties(self, component_data):
        """Add XGen-specific properties to the form."""
        # Density
        density_spin = MSpinBox()
        density_spin.setRange(1, 10000)
        density_spin.setValue(component_data.get("density", 1000))
        density_spin.valueChanged.connect(
            lambda value: self._on_property_changed("density", value)
        )
        self.form_layout.addRow("Density:", density_spin)

        # Length
        length_spin = MDoubleSpinBox()
        length_spin.setRange(0.01, 100.0)
        length_spin.setValue(component_data.get("length", 1.0))
        length_spin.valueChanged.connect(
            lambda value: self._on_property_changed("length", value)
        )
        self.form_layout.addRow("Length:", length_spin)

    def _add_curve_properties(self, component_data):
        """Add curve-specific properties to the form."""
        # Thickness
        thickness_spin = MDoubleSpinBox()
        thickness_spin.setRange(0.001, 10.0)
        thickness_spin.setValue(component_data.get("thickness", 0.1))
        thickness_spin.valueChanged.connect(
            lambda value: self._on_property_changed("thickness", value)
        )
        self.form_layout.addRow("Thickness:", thickness_spin)

        # Subdivisions
        subdiv_spin = MSpinBox()
        subdiv_spin.setRange(1, 10)
        subdiv_spin.setValue(component_data.get("subdivisions", 3))
        subdiv_spin.valueChanged.connect(
            lambda value: self._on_property_changed("subdivisions", value)
        )
        self.form_layout.addRow("Subdivisions:", subdiv_spin)

    def _on_property_changed(self, prop_name, prop_value):
        """Handle property changes.

        Args:
            prop_name (str): Name of the property that changed
            prop_value: New value of the property
        """
        if not self.current_component:
            return

        # Update the component data
        self.current_component[prop_name] = prop_value

        # Notify the manager about the change
        self.manager.update_component(self.current_component)
