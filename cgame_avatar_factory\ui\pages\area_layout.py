"""区域选择组件模块

这个模块提供了一个自定义的区域选择组件，用于DNA融合区域的选择。
包含两个主要类：
- FlowLayout: 自定义流式布局，用于均匀排布按钮
- AreaSelectWidget: 区域选择组件，提供区域选择功能

AreaSelectWidget API:
    属性：
        sig_value_changed: 当区域选择发生变化时发出的信号
            - 参数: (list) 新选中的区域名称列表

    方法：
        current_areas(): 获取当前选中的区域名称列表
            - 返回: (list) 当前选中的区域名称列表，如["all", "nose"]等

        set_value(area_names): 设置当前选中的区域
            - 参数: area_names (list) 要选中的区域名称列表

        set_data(areas): 重新设置按钮数据
            - 参数: areas (list) 新的区域名称列表

使用示例：
    # 创建组件
    widget = AreaSelectWidget(const.MOCK_AREAS_NAME)

    # 获取当前选中区域
    current = widget.current_areas()

    # 设置选中区域
    widget.set_value(["nose", "mouth"])

    # 重新设置数据
    widget.set_data(const.MOCK_MIRROR_AREAS_NAME)

    # 连接信号
    widget.sig_value_changed.connect(on_area_changed)
"""

# Import built-in modules

# Import third-party modules
import dayu_widgets
from dayu_widgets import dayu_theme
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.ui.mixin.style_mixin import MStyleMixin


class FlowLayout(QtWidgets.QLayout):
    """自定义流式布局,用于均匀排布按钮"""

    def __init__(self, parent=None, margin=0, spacing=-1):
        super().__init__(parent)
        self.item_list = []
        self.setContentsMargins(margin, margin, margin, margin)
        self.setSpacing(spacing)

    def __del__(self):
        item = self.takeAt(0)
        while item:
            item = self.takeAt(0)

    def addItem(self, item):
        self.item_list.append(item)

    def count(self):
        return len(self.item_list)

    def itemAt(self, index):
        if 0 <= index < len(self.item_list):
            return self.item_list[index]
        return None

    def takeAt(self, index):
        if 0 <= index < len(self.item_list):
            return self.item_list.pop(index)
        return None

    def expandingDirections(self):
        return QtCore.Qt.Orientations(QtCore.Qt.Orientation(0))

    def hasHeightForWidth(self):
        return True

    def heightForWidth(self, width):
        height = self._do_layout(QtCore.QRect(0, 0, width, 0), True)
        return height

    def setGeometry(self, rect):
        super().setGeometry(rect)
        self._do_layout(rect, False)

    def sizeHint(self):
        return self.minimumSize()

    def minimumSize(self):
        size = QtCore.QSize()
        for item in self.item_list:
            size = size.expandedTo(item.minimumSize())
        margin = self.contentsMargins()
        size += QtCore.QSize(2 * margin.top(), 2 * margin.bottom())
        return size

    def _do_layout(self, rect, test_only):
        x = rect.x()
        y = rect.y()
        line_height = 0
        spacing = self.spacing()

        for item in self.item_list:
            style = item.widget().style()
            layout_spacing_x = style.layoutSpacing(
                QtWidgets.QSizePolicy.PushButton,
                QtWidgets.QSizePolicy.PushButton,
                QtCore.Qt.Horizontal,
            )
            layout_spacing_y = style.layoutSpacing(
                QtWidgets.QSizePolicy.PushButton,
                QtWidgets.QSizePolicy.PushButton,
                QtCore.Qt.Vertical,
            )
            space_x = spacing + layout_spacing_x
            space_y = spacing + layout_spacing_y
            next_x = x + item.sizeHint().width() + space_x
            if next_x - space_x > rect.right() and line_height > 0:
                x = rect.x()
                y = y + line_height + space_y
                next_x = x + item.sizeHint().width() + space_x
                line_height = 0
            if not test_only:
                item.setGeometry(QtCore.QRect(QtCore.QPoint(x, y), item.sizeHint()))
            x = next_x
            line_height = max(line_height, item.sizeHint().height())
        return y + line_height - rect.y()


class AreaSelectWidget(QtWidgets.QFrame):
    """区域选择组件

    提供一个流式布局的按钮组,用于选择DNA融合区域。支持多选，但"all"按钮与其他按钮互斥。
    """

    sig_value_changed = QtCore.Signal(list)

    def __init__(self, areas, parent=None):
        """初始化区域选择组件

        Args:
            areas (list): 区域名称列表
            parent (QWidget, optional): 父组件. Defaults to None.
        """
        super().__init__(parent)
        self.areas = areas
        self.setup_ui()

    def setup_ui(self):
        """初始化UI组件"""
        self.main_layout = QtWidgets.QVBoxLayout()
        self.main_layout.setSpacing(0)
        self.setLayout(self.main_layout)

        self.header_layout = QtWidgets.QHBoxLayout()

        self.title_label = dayu_widgets.MLabel("选择DNA融合区域：")
        self.title_label = MStyleMixin.instance_wrapper(self.title_label)
        self.title_label.foreground_color(dayu_theme.primary_text_color)

        self.header_layout.addStretch(1)
        self.header_layout.addWidget(self.title_label)
        self.header_layout.addStretch(1)

        self.buttons_container = QtWidgets.QWidget()
        self.buttons_layout = FlowLayout(spacing=5)
        self.buttons_container.setLayout(self.buttons_layout)

        self.button_group = QtWidgets.QButtonGroup(self)
        self.button_group.setExclusive(True)

        self._create_buttons()

        self.button_group.buttonClicked.connect(self._on_area_button_clicked)

        self.main_layout.addLayout(self.header_layout)
        self.main_layout.addSpacing(10)
        self.main_layout.addWidget(self.buttons_container)

    def _create_buttons(self):
        for button in self.button_group.buttons():
            self.button_group.removeButton(button)
            self.buttons_layout.removeWidget(button)
            button.deleteLater()

        for area_name in self.areas:
            button = MStyleMixin.instance_wrapper(dayu_widgets.MPushButton(area_name))
            button.small()
            button.frameless().border_radius(5).background_color(dayu_theme.background_color)
            button.setCheckable(True)

            button.clicked.connect(lambda checked=False, name=area_name: self.on_button_clicked(name))

            self.button_group.addButton(button)
            self.buttons_layout.addWidget(button)

    def _on_area_button_clicked(self, clicked_button):
        clicked_text = clicked_button.text()
        is_multi_select = not self.button_group.exclusive()

        if not is_multi_select:
            for button in self.button_group.buttons():
                if button != clicked_button:
                    button.setChecked(False)
                    button.setStyleSheet("")

        if clicked_text == "all":
            for button in self.button_group.buttons():
                if button != clicked_button:
                    button.setChecked(False)
                    button.setStyleSheet("")
        elif is_multi_select:
            if clicked_button.isChecked():
                for button in self.button_group.buttons():
                    if button.text() == "all":
                        button.setChecked(False)
                        button.setStyleSheet("")
                        break

        if clicked_button.isChecked():
            clicked_button.setStyleSheet("background-color: #4A90E2;")
        else:
            clicked_button.setStyleSheet("")

        selected_areas = [btn.text() for btn in self.button_group.buttons() if btn.isChecked()]
        self.sig_value_changed.emit(selected_areas)

    def on_button_clicked(self, name):
        pass

    def current_areas(self):
        return [button.text() for button in self.button_group.buttons() if button.isChecked()]

    def set_value(self, area_names):
        if "all" in area_names:
            area_names = ["all"]

        for button in self.button_group.buttons():
            if button.text() in area_names:
                button.setChecked(True)
                button.setStyleSheet("background-color: #4A90E2;")
            else:
                button.setChecked(False)
                button.setStyleSheet("")

    def set_data(self, areas):
        self.areas = areas
        self._create_buttons()

    def set_exclusive(self, exclusive):
        self.button_group.setExclusive(exclusive)


class AreaSelectWidgetNew(QtWidgets.QFrame):
    sig_value_changed = QtCore.Signal(list)

    def __init__(self, areas, parent=None):
        super().__init__(parent)
        self.areas = areas
        self.setup_ui()

    def setup_ui(self):
        self.main_layout = QtWidgets.QVBoxLayout()
        self.main_layout.setSpacing(0)
        self.setLayout(self.main_layout)

        self.header_layout = QtWidgets.QHBoxLayout()
        self.header_layout.setSpacing(5)

        self.title_label = dayu_widgets.MLabel("区域选择")
        self.title_label = MStyleMixin.instance_wrapper(self.title_label)
        self.title_label.foreground_color(dayu_theme.primary_text_color)

        self.header_layout.addStretch(1)
        self.header_layout.addWidget(self.title_label)
        self.header_layout.addStretch(1)

        self.buttons_layout = QtWidgets.QHBoxLayout()
        self.buttons_layout.setSpacing(5)

        self.button_group = QtWidgets.QButtonGroup()
        self.button_group.setExclusive(True)

        self.main_layout.addLayout(self.header_layout)
        self.main_layout.addLayout(self.buttons_layout)

        self._create_buttons()

    def _create_buttons(self):
        for button in self.button_group.buttons():
            self.button_group.removeButton(button)
            self.buttons_layout.removeWidget(button)
            button.deleteLater()

        for area in self.areas:
            button = dayu_widgets.MToolButton()
            button.setText(area)
            button.setCheckable(True)
            button.clicked.connect(lambda checked, btn=button: self._on_area_button_clicked(btn))
            self.button_group.addButton(button)
            self.buttons_layout.addWidget(button)

    def _on_area_button_clicked(self, clicked_button):
        clicked_text = clicked_button.text()
        is_multi_select = not self.button_group.exclusive()

        if not is_multi_select:
            for button in self.button_group.buttons():
                if button != clicked_button:
                    button.setChecked(False)
                    button.setStyleSheet("")

        if clicked_text == "all":
            for button in self.button_group.buttons():
                if button != clicked_button:
                    button.setChecked(False)
                    button.setStyleSheet("")
        elif is_multi_select and clicked_button.isChecked():
            for button in self.button_group.buttons():
                if button.text() == "all":
                    button.setChecked(False)
                    button.setStyleSheet("")
                    break

        if clicked_button.isChecked():
            clicked_button.setStyleSheet("background-color: #4A90E2;")
        else:
            clicked_button.setStyleSheet("")

        selected_areas = [btn.text() for btn in self.button_group.buttons() if btn.isChecked()]
        self.sig_value_changed.emit(selected_areas)

    def set_exclusive(self, exclusive):
        self.button_group.setExclusive(exclusive)

    def update_areas(self, areas):
        self.areas = areas
        self._create_buttons()

    def set_selected_areas(self, area_names):
        for button in self.button_group.buttons():
            if button.text() in area_names:
                button.setChecked(True)
                button.setStyleSheet("background-color: #4A90E2;")
            else:
                button.setChecked(False)
                button.setStyleSheet("")
