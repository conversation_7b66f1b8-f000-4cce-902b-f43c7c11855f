# Import built-in modules
import json
import logging
from math import cos
from math import pi
from math import sin

# Import third-party modules
import dayu_widgets
from dayu_widgets import dayu_theme
from dayu_widgets.mixin import hover_shadow_mixin
from dna import DataLayer_Definition
from qtpy import QtCore
from qtpy import QtGui
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory import utils
from cgame_avatar_factory.api import dna_utils as dna
from cgame_avatar_factory.ui.dna_merge_ring.weight_label import WeightLabel
from cgame_avatar_factory.ui.mixin.animated_mixin import geo_animated_mixin
from cgame_avatar_factory.ui.mixin.style_mixin import MStyleMixin


@geo_animated_mixin
@hover_shadow_mixin
class CircleWidget(QtWidgets.QWidget):
    sig_circle_widget_clicked = QtCore.Signal()
    sig_weight_changed = QtCore.Signal(float)

    def __init__(self, image_path, radius=50, parent=None):
        super().__init__(parent)
        self.radius = radius
        self.image_path = image_path
        self._show_weight = False
        self.logger = logging.getLogger(__name__)

        self.weight_label = WeightLabel(self)
        self.weight_label.setMinimumSize(70, 25)
        self.weight_label.sig_weight_changed.connect(self._on_weight_changed)
        self.weight_label.hide()

        self.image = QtGui.QPixmap(self.image_path)
        self.image = self.image.scaled(
            2 * self.radius,
            2 * self.radius,
            QtCore.Qt.IgnoreAspectRatio,
            QtCore.Qt.SmoothTransformation,
        )

        self.setFixedSize(2 * self.radius, 2 * self.radius + self.weight_label.height())

        self.setMouseTracking(True)
        self.update_circle_image()

    def update_weight(self, weight):
        if hasattr(self, "weight_label"):
            self.weight_label.update_weight(weight)

    def _on_weight_changed(self, weight):
        self.sig_weight_changed.emit(weight)

    def set_weight_visible(self, visible):
        if self._show_weight != visible:
            self._show_weight = visible
            self.weight_label.setVisible(visible)
            if visible:
                self.weight_label.move(
                    (self.width() - self.weight_label.width()) // 2,
                    0,
                )

            self.update()

    def update_circle_image(self):
        """Update the circular image."""
        mask = QtGui.QBitmap(self.image.size())
        mask.fill(QtCore.Qt.color0)

        painter = QtGui.QPainter(mask)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)
        painter.setPen(QtCore.Qt.color1)
        painter.setBrush(QtCore.Qt.color1)
        painter.drawEllipse(0, 0, 2 * self.radius, 2 * self.radius)
        painter.end()

        self.image.setMask(mask)

    def paintEvent(self, event):
        """Paint the circular image."""
        if self.image.isNull():
            return

        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)
        painter.setRenderHint(QtGui.QPainter.SmoothPixmapTransform)

        x = (self.width() - self.image.width()) // 2
        y = (self.height() - self.image.height()) // 2
        if self._show_weight:
            y = self.weight_label.height() + (self.height() - self.weight_label.height() - self.image.height()) // 2

        painter.drawPixmap(x, y, self.image)

    def mousePressEvent(self, event):
        parent = self.parent()
        if parent and hasattr(parent, "_point_pos") and hasattr(parent, "_point_radius"):
            point_rect = QtCore.QRectF(
                parent._point_pos.x() - parent._point_radius,
                parent._point_pos.y() - parent._point_radius,
                2 * parent._point_radius,
                2 * parent._point_radius,
            )
            widget_pos = self.mapTo(parent, event.pos())
            if point_rect.contains(widget_pos):
                event.ignore()
                return

        if event.button() == QtCore.Qt.LeftButton:
            self.sig_circle_widget_clicked.emit()
            event.accept()

    def _on_weight_changed(self, weight):
        """处理权重变化"""
        self.sig_weight_changed.emit(weight)


class CircleSlotWidget(CircleWidget):
    """Allow us to create empty circle."""

    def __init__(self, image_path=None, radius=50, parent=None, empty_color=None):
        if image_path is not None:
            super().__init__(image_path, radius, parent)
        else:
            super().__init__("", radius, parent)
            self.image = QtGui.QPixmap()
            self.setFixedSize(2 * self.radius, 2 * self.radius)

        self._empty_color = empty_color or QtGui.QColor(75, 75, 75)

    def enterEvent(self, event):
        pass

    def leaveEvent(self, event):
        pass

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        if not self.image.isNull():
            painter.drawPixmap(0, 0, self.image)
        else:
            painter.setBrush(self._empty_color)
            painter.setPen(QtCore.Qt.NoPen)
            painter.drawEllipse(0, 0, self.width(), self.height())
            painter.setPen(QtGui.QColor(55, 55, 55))
            font = QtGui.QFont("Arial", 14, QtGui.QFont.Bold)
            painter.setFont(font)
            font_metrics = QtGui.QFontMetrics(font)
            text_width = font_metrics.width("empty")
            text_height = font_metrics.height()
            painter.drawText((self.width() - text_width) / 2.0, (self.height() + text_height / 2.0) / 2.0, "empty")


@geo_animated_mixin
class NoHoverCircleSlotWidget(CircleSlotWidget):
    """A version of CircleSlotWidget without hover effects"""

    def __init__(self, image_path=None, radius=50, parent=None, empty_color=None):
        super().__init__(image_path, radius, parent, empty_color)
        self.setMouseTracking(False)

    def enterEvent(self, event):
        pass

    def leaveEvent(self, event):
        pass


class DNACircleSlotWidget(NoHoverCircleSlotWidget):
    sig_clicked = QtCore.Signal(dict)
    sig_info_changed = QtCore.Signal(dict)
    sig_right_clicked = QtCore.Signal(dict)
    sig_weight_changed = QtCore.Signal(dict, float)

    def __init__(self, dna_data, image_path=None, radius=50, empty_color=None, parent=None):
        if image_path:
            super().__init__(image_path, radius, parent)
        else:
            super().__init__("", radius, parent)
            self.image = QtGui.QPixmap()
            self.setFixedSize(2 * self.radius, 2 * self.radius)

        self._empty_color = empty_color or QtGui.QColor(75, 75, 75)
        self._dna_data = {key: dna_data[key] for key in ["dna_file_path", "thumbnail_file_path", "dna_order"]}
        self._dna_data["collapsed_model"] = dna_data.get("collapsed_model", False)
        self.setAcceptDrops(True)

        if hasattr(self, "weight_label"):
            self.weight_label.sig_weight_changed.connect(self._on_weight_changed)

    def update_weight(self, weight):
        super().update_weight(weight)

    def _on_weight_changed(self, weight):
        self.sig_weight_changed.emit(self._dna_data, weight)

    @property
    def dna_file_path(self):
        return self._dna_data.get("dna_file_path", None)

    def get_collapsed_model(self):
        result = self._dna_data.get("collapsed_model", False)
        return result

    @property
    def thumbnail_file_path(self):
        return self._dna_data.get("thumbnail_file_path", None)

    @property
    def dna_order(self):
        return self._dna_data.get("dna_order", None)

    @property
    def dna_data(self):
        return self._dna_data

    def dragEnterEvent(self, event):
        if event.mimeData().hasText():
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event):
        json_str = event.mimeData().text()
        data_dict = json.loads(json_str)

        new_data_dict = self._dna_data.copy()
        new_data_dict.update(data_dict)
        self.sig_info_changed.emit(new_data_dict)

        event.accept()
        event.acceptProposedAction()

    def update_dna_data(self, data_dict):
        self._dna_data = {key: data_dict[key] for key in ["dna_file_path", "thumbnail_file_path", "dna_order"]}
        if data_dict.get("thumbnail_file_path"):
            self.image = QtGui.QPixmap(data_dict["thumbnail_file_path"])
            self.image = self.image.scaled(
                2 * self.radius,
                2 * self.radius,
                QtCore.Qt.IgnoreAspectRatio,
                QtCore.Qt.SmoothTransformation,
            )
        self.sig_info_changed.emit(self._dna_data)
        self.update()

    def mousePressEvent(self, event):
        event.ignore()

    def contextMenuEvent(self, event):
        event.ignore()

    def paintEvent(self, event):
        """Paint the empty circle or image."""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        if not self.image.isNull():
            x = (self.width() - self.image.width()) // 2
            y = self.weight_label.height() + (self.height() - self.weight_label.height() - self.image.height()) // 2
            painter.drawPixmap(x, y, self.image)
        else:
            painter.setBrush(self._empty_color)
            painter.setPen(QtCore.Qt.NoPen)
            x = (self.width() - 2 * self.radius) // 2
            y = self.weight_label.height() + (self.height() - self.weight_label.height() - 2 * self.radius) // 2
            painter.drawEllipse(x, y, 2 * self.radius, 2 * self.radius)


class RingWidget(QtWidgets.QWidget):
    sig_component_added = QtCore.Signal()
    sig_component_removed = QtCore.Signal()
    sig_component_cleared = QtCore.Signal()

    def __init__(self, radius=200, line_width=20, color=dayu_theme.background_out_color, parent=None):
        super().__init__(parent)
        self._ring_radius = radius
        self._ring_color = color
        self.line_width = line_width

        self._component_cls = CircleSlotWidget

        self._components = []

        self.setAcceptDrops(True)
        self.setFixedSize(800, 800)

        self.setup_ui()
        self.init_hint_label()

    def setup_ui(self):
        self.setup_layout()
        self.setup_widgets()

    def setup_layout(self):
        self.main_layout = QtWidgets.QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(self.main_layout)

    def setup_widgets(self):
        pass

    def init_hint_label(self):
        self.hint_label = MStyleMixin.cls_wrapper(dayu_widgets.MLabel)("请从右侧拖入DNA").secondary()
        self.hint_label.foreground_color(dayu_theme.background_out_color)
        self.main_layout.addWidget(self.hint_label)
        self.hint_label.adjustSize()
        self.hint_label.setAlignment(QtCore.Qt.AlignCenter)
        self.update_hint_label()

    def update_hint_label(self):
        if not self._components:
            self.hint_label.show()
            component_center_x, component_center_y = utils.widget_center_pos(self.hint_label)
            label_center_x = self.center_x - component_center_x
            label_center_y = self.center_y - component_center_y
            self.hint_label.move(label_center_x, label_center_y)
        else:
            self.hint_label.hide()

    def add_component_to_center(self, widget):
        self.add_component_to_pos(widget, self.center_x, self.center_y)

    def add_component_to_pos(self, widget, pos_x, pos_y):
        self._components.append(widget)
        widget.setParent(self)
        component_center_x, component_center_y = utils.widget_center_pos(widget)
        widget.setGeometry(
            QtCore.QRect(
                pos_x - component_center_x,
                pos_y - component_center_y,
                widget.width(),
                widget.height(),
            ),
        )
        widget.update()
        widget.show()
        self.update()
        self.update_components()
        self.update_hint_label()
        self.sig_component_added.emit()

    def remove_component(self):
        if self._components:
            widget = self._components.pop()
            widget.setParent(None)
            widget.deleteLater()
            self.update_components()
            self.update_hint_label()
            self.sig_component_removed.emit()

    def clear_components(self):
        for widget in self._components:
            widget.setParent(None)
            widget.deleteLater()
        self._components = []
        self.update()
        self.update_hint_label()
        self.sig_component_cleared.emit()

    def update_components(self):
        if not self._components:
            return

        base_component = self._components[0]
        base_center_x, base_center_y = utils.widget_center_pos(base_component)
        base_component.setGeometry(
            QtCore.QRect(
                self.center_x - base_center_x,
                self.center_y - base_center_y,
                base_component.width(),
                base_component.height(),
            ),
        )

        if len(self._components) == 1:
            return

        remaining_components = self._components[1:]
        angle_step = 360.0 / len(remaining_components)
        start_angle = -90

        if len(remaining_components) <= 3:
            radius = self._ring_radius
        else:
            radius = self._ring_radius * 1.2

        for i, component in enumerate(remaining_components):
            angle = start_angle + angle_step * i
            radians = angle * (pi / 180.0)
            component_center_x, component_center_y = utils.widget_center_pos(component)
            x = self.center_x + radius * cos(radians) - component_center_x
            y = self.center_y + radius * sin(radians) - component_center_y
            component.setGeometry(QtCore.QRect(x, y, component.width(), component.height()))

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        pen = QtGui.QPen(self._ring_color, self.line_width)
        painter.setPen(pen)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)
        painter.drawEllipse(
            self.center_x - self._ring_radius,
            self.center_y - self._ring_radius,
            self._ring_radius * 2.0,
            self._ring_radius * 2.0,
        )
        super().paintEvent(event)

    @property
    def center_pos(self):
        return self.center_x, self.center_y

    @property
    def center_x(self):
        return self.width() / 2.0

    @property
    def center_y(self):
        return self.height() / 2.0

    @QtCore.Slot()
    def slot_component_clicked(self, component):
        pass


class DragDropRingWidget(RingWidget):
    def dragEnterEvent(self, event):
        if event.mimeData().hasText():
            event.acceptProposedAction()
        elif event.mimeData().hasFormat("text/uri-list"):
            file_list = [url.toLocalFile() for url in event.mimeData().urls() if url.toLocalFile()]
            if not file_list:
                event.ignore()
            else:
                event.accept()
        else:
            event.ignore()

    def dropEvent(self, event):
        position = event.pos()
        json_str = event.mimeData().text()
        data_dict = json.loads(json_str)

        widget = self._component_cls(image_path=data_dict["thumbnail_file_path"], parent=self)
        self.add_component_to_pos(widget, position.x(), position.y())
        event.acceptProposedAction()


class DNARingWidget(DragDropRingWidget):
    sig_dna_added = QtCore.Signal(dict)
    sig_dna_removed = QtCore.Signal(dict)
    sig_dna_changed = QtCore.Signal(dict)
    sig_dna_clicked = QtCore.Signal(dict)

    def __init__(self, radius=200, line_width=20, color=QtGui.QColor(55, 55, 55), parent=None):
        super().__init__(radius=radius, line_width=line_width, color=color, parent=parent)
        self._component_cls = DNACircleSlotWidget
        self.logger = logging.getLogger(__name__)

    def dropEvent(self, event):
        position = event.pos()
        json_str = event.mimeData().text()
        data_dict = json.loads(json_str)
        data_dict["dna_order"] = len(self._components)

        dna_path = data_dict.get("dna_file_path", "未知路径")
        if self.is_dna_allowed(dna_path):
            widget = self.create_component(data_dict)
            self.add_component_to_pos(widget, position.x(), position.y())
        else:
            self.logger.info(f"DNA {dna_path} 不允许被拖入")

        event.acceptProposedAction()

    def create_component(self, dna_data):
        widget = self._component_cls(dna_data=dna_data, image_path=dna_data.get("thumbnail_file_path"), parent=self)
        return widget

    def add_component_to_pos(self, widget, pos_x, pos_y):
        self._components.append(widget)
        widget.setParent(self)

        component_center_x, component_center_y = utils.widget_center_pos(widget)
        widget.setGeometry(
            QtCore.QRect(
                pos_x - component_center_x,
                pos_y - component_center_y,
                widget.width(),
                widget.height(),
            ),
        )
        widget.update()
        widget.show()
        self.update()
        self.connect_components_signals(widget)
        self.update_components()
        self.update_hint_label()
        self.sig_dna_added.emit(widget.dna_data)

    def remove_component(self):
        if self._components:
            widget = self._components.pop()
            widget.setParent(None)
            widget.deleteLater()
            self.update_components()
            self.update_hint_label()
            self.sig_dna_removed.emit(widget.dna_data)

    def connect_components_signals(self, widget):
        widget.sig_right_clicked.connect(self.slot_component_right_clicked)
        widget.sig_clicked.connect(self.slot_component_clicked)
        widget.sig_info_changed.connect(self.slot_component_changed)

    @property
    def components(self):
        return self._components

    @QtCore.Slot(dict)
    def slot_component_clicked(self, dna_data):
        self.sig_dna_clicked.emit(dna_data)

    @QtCore.Slot(dict)
    def slot_component_changed(self, dna_data):
        self.sig_dna_changed.emit(dna_data)

    @QtCore.Slot(dict)
    def slot_component_right_clicked(self, dna_data):
        pass

    def is_dna_allowed(self, dna_path):
        """check DNA if allowed to drag in

        Args:
            dna_path (str): DNA file path

        Returns:
            bool: if allowed to drag in, return True, otherwise return False
        """

        if not hasattr(self, "_source_mesh_names"):
            self._source_mesh_names = set(dna.TemplateDna.get_mesh_name())

        try:
            reader = dna.load_dna_calib(dna_path, DataLayer_Definition)
            mesh_names = [reader.getMeshName(i) for i in range(reader.getMeshCount())]
            missing_meshes = [mesh for mesh in mesh_names if mesh not in self._source_mesh_names]

            if missing_meshes:
                QtWidgets.QMessageBox.warning(
                    self,
                    "无法添加DNA",
                    f"角色缺失模型{missing_meshes[0]}，无法参与融合",
                    QtWidgets.QMessageBox.Ok,
                )
                return False

            return True

        except Exception as e:
            QtWidgets.QMessageBox.warning(
                self,
                "无法添加DNA",
                f"加载DNA文件失败: {str(e)}",
                QtWidgets.QMessageBox.Ok,
            )
            return False
