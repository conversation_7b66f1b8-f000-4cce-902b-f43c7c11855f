"""Base Hair Tab Module.

This module provides the base class for all hair type tabs in the Hair Studio tool.
"""

# Import standard library
import logging

# Import Qt modules
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList
from cgame_avatar_factory.hair_studio.ui.editor_area import Editor<PERSON>rea
from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import AssetLibrary


class BaseHairTab(QtWidgets.QWidget):
    """Base class for hair type tabs.
    
    This class provides common functionality for all hair type tabs (Card, XGen, Curve).
    Each tab contains three main areas: editor area, component list, and asset library.
    """
    
    def __init__(self, hair_type, hair_manager, parent=None):
        """Initialize the BaseHairTab.
        
        Args:
            hair_type (str): Type of hair ('card', 'xgen', 'curve')
            hair_manager (HairManager): The hair manager instance
            parent (QWidget, optional): The parent widget. Defaults to None.
        """
        super(<PERSON><PERSON><PERSON>Tab, self).__init__(parent)
        self.hair_type = hair_type
        self._hair_manager = hair_manager
        self._selected_component = None
        self.object_name = '{}HairTab'.format(hair_type.capitalize())
        self.setObjectName(self.object_name)
        
        # Initialize logger
        self._logger = logging.getLogger(__name__)
        
        # Initialize UI components
        self.setup_ui()
        
        # Connect signals
        self._connect_signals()
        
        # Initial refresh (moved after UI setup)
        # Refresh is now handled by the setup_ui method
    
    def setup_ui(self):
        """Set up the user interface components."""
        try:
            # Main layout
            main_layout = QtWidgets.QHBoxLayout(self)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(2)
            
            # Create main areas with the shared hair manager
            self.editor_area = EditorArea(self.hair_type, self._hair_manager, self)
            self.component_list = ComponentList(self.hair_type, self._hair_manager, self)
            self.asset_library = AssetLibrary(self.hair_type, self._hair_manager, self)
            
            # Add widgets to layout with stretch factors
            main_layout.addWidget(self.editor_area, stretch=4)      # 40% width
            main_layout.addWidget(self.component_list, stretch=3)   # 30% width
            main_layout.addWidget(self.asset_library, stretch=3)    # 30% width
            
            # Initial refresh after UI is set up
            self.refresh_asset_library()
            
        except Exception as e:
            self._logger.error("Failed to set up UI: %s", str(e), exc_info=True)
            QtWidgets.QMessageBox.critical(self, "Error", "Failed to initialize tab UI: {}".format(str(e)))
    
    def _connect_signals(self):
        """Connect signals between components."""
        try:
            # When an asset is selected in the library, create a new component
            self.asset_library.asset_selected.connect(self._on_asset_selected)
            
            # When a component is selected in the list, update the editor area
            self.component_list.component_selected.connect(self._on_component_selected)
            
            # When a component is deleted from the list
            self.component_list.component_deleted.connect(self._on_component_deleted)
            
        except Exception as e:
            self._logger.error("Failed to connect signals: %s", str(e), exc_info=True)
    
    def _on_asset_selected(self, asset_id):
        """Handle asset selection from the asset library.
        
        Args:
            asset_id (str): ID of the selected asset
        """
        try:
            # Create a new component from the selected asset
            component = self._hair_manager.create_component(asset_id)
            if component:
                # Select the new component
                self._hair_manager.select_component(component.id)
                
        except Exception as e:
            self._logger.error("Error creating component: %s", str(e), exc_info=True)
            QtWidgets.QMessageBox.critical(self, "Error", "Failed to create component: {}".format(str(e)))
    
    def _on_component_selected(self, component_id):
        """Handle component selection from the component list.
        
        Args:
            component_id (str): ID of the selected component
        """
        try:
            # Update the hair manager's selected component
            self._hair_manager.select_component(component_id)
            
            # Get the component details
            component = self._hair_manager.get_component(component_id)
            if component:
                # Update the editor area with the component details
                self.editor_area.set_component(component)
                
        except Exception as e:
            self._logger.error("Error selecting component: %s", str(e), exc_info=True)
    
    def _on_component_deleted(self, component_id):
        """Handle component deletion from the component list.
        
        Args:
            component_id (str): ID of the component to delete
        """
        try:
            # Delete the component through the hair manager
            if not self._hair_manager.delete_component(component_id):
                self._logger.warning("Failed to delete component: %s", component_id)
                
        except Exception as e:
            self._logger.error("Error deleting component: %s", str(e), exc_info=True)
    
    def refresh_asset_library(self):
        """Refresh the asset library with the latest data."""
        try:
            # Get assets filtered by the current hair type
            assets = self._hair_manager.get_assets(asset_type=self.hair_type)
            
            # Update the asset library
            self.asset_library.update_assets(assets)
            
        except Exception as e:
            self._logger.error("Error refreshing asset library: %s", str(e), exc_info=True)
    
    def update_component_list(self, components):
        """Update the component list with the given components.
        
        Args:
            components (list): List of HairComponent objects
        """
        try:
            # Filter components by the current hair type
            filtered_components = [
                comp for comp in components 
                if comp.type == self.hair_type
            ]
            
            # Update the component list
            self.component_list.update_components(filtered_components)
            
        except Exception as e:
            self._logger.error("Error updating component list: %s", str(e), exc_info=True)
    
    def set_selected_component(self, component):
        """Set the currently selected component.
        
        Args:
            component (HairComponent or None): The component to select, or None to clear selection
        """
        try:
            self._selected_component = component
            
            if component:
                # Update the editor area
                self.editor_area.set_component(component)
                
                # Select the component in the list
                self.component_list.select_component(component.id)
            else:
                # Clear the editor area
                self.editor_area.clear()
                
                # Clear the selection in the list
                self.component_list.clear_selection()
                
        except Exception as e:
            self._logger.error("Error setting selected component: %s", str(e), exc_info=True)
    
    def get_hair_type(self):
        """Get the hair type of this tab.
        
        Returns:
            str: The hair type ('card', 'xgen', or 'curve')
        """
        return self.hair_type
    
    def get_hair_manager(self):
        """Get the hair manager instance.
        
        Returns:
            HairManager: The hair manager instance
        """
        return self._hair_manager